package in.gov.cgg.repository;



import java.sql.DriverManager;
import java.sql.Connection;
;
public class Connjdbc {
	public static Connection  getConnection(String databasedriverclassNname,String databaseurl,String databaseusername,String databasepassword) {

		Connection con = null;
		try{
			Class.forName(databasedriverclassNname);
			con = DriverManager.getConnection(databaseurl,databaseusername,databasepassword); 
			
	 		
			
		}catch(Exception e){
			e.printStackTrace();
		}
		return con;
	}
	
}

