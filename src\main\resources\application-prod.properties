
 #database-config
database.driver=org.postgresql.Driver
spring.datasource.url=*************************************************
spring.datasource.username=ticketing_system
spring.datasource.password=ticketing_system@25
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
# Show or not log for each sql query
spring.jpa.show-sql=true
# JPA specific configs
  
#upload paths
FILES_UPLOAD_PATH_OFFICER=/Uploads/IssueTracker/OfficerAttachments/
FILES_UPLOAD_PATH_ADMIN=/Uploads/IssueTracker/AdminAttachments/

#context-config
 spring.main.allow-bean-definition-overriding=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

#view-config
spring.mvc.view.prefix=/views/
spring.mvc.view.suffix=.jsp
 
# schema will be automatically created afresh for every start of application
spring.jpa.hibernate.ddl-auto=update
server.servlet.session.timeout=60m
spring.jpa.hibernate.use-new-id-generator-mappings=true
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQL9Dialect
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=5MB
# Set session timeout to 30 minutes


undertaking.upload.path=/Uploads/UT_files/uploadedDocs/
#email
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=nowf kqez zmos ayfk
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

spring.datasource.dataSourceClassName=org.postgresql.ds.PGSimpleDataSource
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.minimumIdle=1
spring.datasource.maximumPoolSize=5
spring.datasource.idleTimeout=30000
spring.datasource.poolName=SpringBootJPAHikariCP
spring.datasource.maxLifetime=2000000
spring.datasource.connectionTimeout=120000
spring.jpa.hibernate.connection.provider_class=org.hibernate.hikaricp.internal.HikariCPConnectionProvider
spring.datasource.allowMultiQueries=true