package in.gov.cgg.util;

import java.util.Properties;

import javax.mail.Message;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.springframework.stereotype.Component;

@Component
public class SimpleMailSender {

	public void sendMail(String email, String notificationName,String projectStatus) {
		final String host = "mail.tspsc.gov.in";
		final String user = "tspscotps";
		final String password = "hydts#tspsc36";
		final String fromMailId = "<EMAIL>";
		final String subject = "TSPSC PROJECT STATUS";
		String toMailId = null;
		String content = "";
		String contentType = "text/html";

		System.out.println("Email -----------------------" + email);

		Properties props = new Properties();
		props.put("mail.smtp.host", host);
		props.put("mail.smtp.auth", "true");
		props.put("mail.smtp.port", "587");
		props.put("mail.smtp.starttls.enable", "true");


		Session session = Session.getInstance(props, new javax.mail.Authenticator() {
			public PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(user, password);
			}
		});
		try {
			// String toMailId = "<EMAIL>";
			/* if (!email.isEmpty()) { */
			toMailId = email;
			content = "Dear Sir/Madam,<br><br>";
			content = content + "This is notify that"+". <br><br>";
			content = content + notificationName+projectStatus +"<br><br>";
			content = content + "Note: This is an auto generated mail. Please don't reply back.";

			MimeMessage message = new MimeMessage(session);
			message.setFrom(new InternetAddress(fromMailId));

			// For Testing Only.
			// message.addRecipient(Message.RecipientType.TO, new
			// InternetAddress(toMailId));
			message.addRecipient(Message.RecipientType.TO, new InternetAddress(toMailId));

			message.setSubject(subject);
			message.setContent(content, contentType);
			Transport.send(message);
			System.out.println("Sent message successfully....");
			/* } *//*
					 * else { toMailId = "<EMAIL>"; content =
					 * "Hi Team,<br><br> This is a Failure OTP Email For Mobile No...." + mobileno +
					 * ".<br><br>"; content = content + "Please send the OTP to the candidate..." +
					 * OTP;
					 * 
					 * MimeMessage message = new MimeMessage(session); message.setFrom(new
					 * InternetAddress(fromMailId));
					 * 
					 * //For Testing Only. //message.addRecipient(Message.RecipientType.TO, new
					 * InternetAddress(toMailId)); message.addRecipient(Message.RecipientType.TO,
					 * new InternetAddress(toMailId));
					 * 
					 * message.setSubject(subject); message.setContent(content, contentType);
					 * Transport.send(message);
					 * System.out.println("OTP Failed fo the mail id with mobile no...." +
					 * mobileno);
					 * 
					 * System.out.println("Sent message successfully...."); }
					 */
		} catch (Exception mex) {
			toMailId = "<EMAIL>";
			content = "Dear Sir/Madam,<br><br> This is a Failure Email For Notification...." + notificationName + ".<br><br>";
			
			try {
				MimeMessage message = new MimeMessage(session);
				message.setFrom(new InternetAddress(fromMailId));

				// For Testing Only.
				// message.addRecipient(Message.RecipientType.TO, new
				// InternetAddress(toMailId));
				message.addRecipient(Message.RecipientType.TO, new InternetAddress(toMailId));

				message.setSubject(subject);
				message.setContent(content, contentType);
				Transport.send(message);
				System.out.println("Sent message successfully....");
			} catch (Exception mea) {
				mea.printStackTrace();
			}

		}

	}

}
