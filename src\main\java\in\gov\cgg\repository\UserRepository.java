package in.gov.cgg.repository;

import in.gov.cgg.entity.Employee;
import java.util.List;
import javax.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<Employee, Long> {
  public Employee findByUserName(String userName);

  @Query(
    value = "select * from employee a inner join  employee_level_mapping b on a.id=b.employee_id where level_id=:levelId and b.project_type_id=:projectTypeId",
    nativeQuery = true
  )
  public List<Employee> getEmployeeByProjectTypeLevel(
    Long projectTypeId,
    Long levelId
  );

  /*
   * @Query(
   * value="select b.level_id from employee a inner join  employee_level_mapping b on a.id=b.employee_id where employee_id=:employeeId"
   * ,nativeQuery = true) public int findByEmployeeId(Long employeeId);
   */

  public Employee findAllById(Long employeeId);

  @Query(
    value = "select user_name,id from employee where project_type_id=:projectTypeId",
    nativeQuery = true
  )
  public List<String> finduserNameByProjectTypeId(Long projectTypeId);

  @Query(
    value = "select role_id from employee where id=:id",
    nativeQuery = true
  )
  public Long findroleid(Long id);

  @Query(
    value = "select * from employee where user_name=:username",
    nativeQuery = true
  )
  public int finduserName(String username);

  @Query(
    value = "select * from employee where user_name=:username",
    nativeQuery = true
  )
  public Employee finduser(String username);

  @Modifying
  @Transactional
  @Query(
    value = "update issue_tracker_user set password=:newpassword,real_password=:newpassword where user_id=:empId",
    nativeQuery = true
  )
  public int updateNewPassword(String newpassword, Long empId);

  @Query(
    value = "select password from issue_tracker_user where user_id=:id",
    nativeQuery = true
  )
  public String findPasswordDetails(Long id);

  @Modifying
  @Transactional
  @Query(
    value = "update issue_tracker_user set is_active=true where user_id=:id",
    nativeQuery = true
  )
  public void updateIsActive(Long id);
}
