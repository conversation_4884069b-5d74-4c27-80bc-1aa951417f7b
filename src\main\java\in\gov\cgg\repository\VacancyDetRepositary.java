package in.gov.cgg.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.VraVacancyMaster;

@Repository
public interface VacancyDetRepositary extends JpaRepository<VraVacancyMaster, Long>{

	@Override
	@Query
	public List<VraVacancyMaster> findAll();
	
	VraVacancyMaster findBySno(Long id);

	public int deleteBySno(Long sno);

//	@Query(value=":query",nativeQuery=true)
//	public List<String[]> getpostCategDetails();
}
