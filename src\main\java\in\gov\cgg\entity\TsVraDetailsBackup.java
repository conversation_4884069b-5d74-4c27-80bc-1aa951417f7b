package in.gov.cgg.entity;

import java.io.Serializable;
import java.security.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.NamedQuery;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ser.std.StdKeySerializers.Default;

@Entity
@Table(name="Ts_Vra_details_Backup")
public class TsVraDetailsBackup implements Serializable {

	private static final long serialVersionUID = 1874347319518898894L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VRA_SEQUENCE")
	@SequenceGenerator(name = "VRA_SEQUENCE", sequenceName = "VRA_SEQUENCE", allocationSize = 1)
	private Long sno;
	
	@Column(name = "district_code")
	private Integer districtCode;
	
	@Column(name = "mandal_code")
	private Integer mandalCode;
	
	@Column(name = "village_code")
	private Integer villageCode;
	
	@Column(name = "name")
	private String name;
	
	@Column(name = "father_name")
	private String fatherName;
	
	@Column(name = "gender")
	private String gender;
	
	@Column(name = "religion")
	private String religion;
	
	@Column(name = "community")
	private String community;
	
	@Column
	@DateTimeFormat(pattern = "dd/MM/yyyy")
	private Date dateOfBirth;
	
	@Column(name = "present_age")
	private Integer presentAge;
	
	@Column(name = "type_of_recruitment")
	private String typeOfRecruitment;
	
	@Column(name = "appointment_prior_to")
	private String appointmentPriorTo;

	@Column(name = "appointment_after")
	private String appointmentAfter;
	
	@Column
	@DateTimeFormat(pattern = "dd/MM/yyyy")
	private Date dateOfAppointmnet;
	
	@Column(name = "education_qualification")
	private String educationQualification;
	
	@Column(name = "exact_education_qualification")
	private String exactEducationQualification;
	
	@Column(name = "present_status")
	private String presentStatus;
	
	@Column(name = "remarks")
	private String remarks;
	
	@Column(name = "under_taking_document")
	private String underTakingDocument;
	
	@Column(name = "under_taking_given")
	private String underTakingGiven;
	
	@DateTimeFormat(pattern = "dd/MM/yyyy")
	@Column(name="under_taking_given_date")
	private Date underTakingGivenDate;
	
	@Column(name = "eligible_for_reg")
	private String eligibleForReg;
	
	@Column(name = "native_district_code")
	private Integer nativeDistrictCode;
	
	@Column(name = "native_mandal_code")
	private Integer nativeMandalCode;
	
	@Column(name = "native_village_code")
	private Integer nativeVillageCode;
	
	@Column(name = "updated_time")
	private LocalDateTime  updatedTime =LocalDateTime.now();
	
	@Column(name = "updated_by")
	private String updatedby;
	
	public LocalDateTime getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(LocalDateTime updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getUpdatedby() {
		return updatedby;
	}

	public void setUpdatedby(String updatedby) {
		this.updatedby = updatedby;
	}

	@Transient
	private String dob;
	
	@Transient
	private String doa;
	
	@Transient
	private MultipartFile document;

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public Integer getDistrictCode() {
		return districtCode;
	}

	public void setDistrictCode(Integer districtCode) {
		this.districtCode = districtCode;
	}

	public Integer getMandalCode() {
		return mandalCode;
	}

	public void setMandalCode(Integer mandalCode) {
		this.mandalCode = mandalCode;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFatherName() {
		return fatherName;
	}

	public void setFatherName(String fatherName) {
		this.fatherName = fatherName;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getReligion() {
		return religion;
	}

	public void setReligion(String religion) {
		this.religion = religion;
	}

	public String getCommunity() {
		return community;
	}

	public void setCommunity(String community) {
		this.community = community;
	}

	public Date getDateOfBirth() {
		return dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	public Integer getPresentAge() {
		return presentAge;
	}

	public void setPresentAge(Integer presentAge) {
		this.presentAge = presentAge;
	}

	public String getTypeOfRecruitment() {
		return typeOfRecruitment;
	}

	public void setTypeOfRecruitment(String typeOfRecruitment) {
		this.typeOfRecruitment = typeOfRecruitment;
	}

	public String getAppointmentPriorTo() {
		return appointmentPriorTo;
	}

	public void setAppointmentPriorTo(String appointmentPriorTo) {
		this.appointmentPriorTo = appointmentPriorTo;
	}

	public String getAppointmentAfter() {
		return appointmentAfter;
	}

	public void setAppointmentAfter(String appointmentAfter) {
		this.appointmentAfter = appointmentAfter;
	}

	public Date getDateOfAppointmnet() {
		return dateOfAppointmnet;
	}

	public void setDateOfAppointmnet(Date dateOfAppointmnet) {
		this.dateOfAppointmnet = dateOfAppointmnet;
	}

	public String getEducationQualification() {
		return educationQualification;
	}

	public void setEducationQualification(String educationQualification) {
		this.educationQualification = educationQualification;
	}

	public String getExactEducationQualification() {
		return exactEducationQualification;
	}

	public void setExactEducationQualification(String exactEducationQualification) {
		this.exactEducationQualification = exactEducationQualification;
	}

	public String getPresentStatus() {
		return presentStatus;
	}

	public void setPresentStatus(String presentStatus) {
		this.presentStatus = presentStatus;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getDob() {
		return dob;
	}

	public void setDob(String dob) {
		this.dob = dob;
	}

	public String getDoa() {
		return doa;
	}

	public void setDoa(String doa) {
		this.doa = doa;
	}

	public String getUnderTakingDocument() {
		return underTakingDocument;
	}

	public void setUnderTakingDocument(String underTakingDocument) {
		this.underTakingDocument = underTakingDocument;
	}

	public MultipartFile getDocument() {
		return document;
	}

	public void setDocument(MultipartFile document) {
		this.document = document;
	}

	public Integer getVillageCode() {
		return villageCode;
	}

	public void setVillageCode(Integer villageCode) {
		this.villageCode = villageCode;
	}

	public String getUnderTakingGiven() {
		return underTakingGiven;
	}

	public void setUnderTakingGiven(String underTakingGiven) {
		this.underTakingGiven = underTakingGiven;
	}

	public Date getUnderTakingGivenDate() {
		return underTakingGivenDate;
	}

	public void setUnderTakingGivenDate(Date underTakingGivenDate) {
		this.underTakingGivenDate = underTakingGivenDate;
	}

	public String getEligibleForReg() {
		return eligibleForReg;
	}

	public void setEligibleForReg(String eligibleForReg) {
		this.eligibleForReg = eligibleForReg;
	}

	public Integer getNativeDistrictCode() {
		return nativeDistrictCode;
	}

	public void setNativeDistrictCode(Integer nativeDistrictCode) {
		this.nativeDistrictCode = nativeDistrictCode;
	}

	public Integer getNativeMandalCode() {
		return nativeMandalCode;
	}

	public void setNativeMandalCode(Integer nativeMandalCode) {
		this.nativeMandalCode = nativeMandalCode;
	}

	public Integer getNativeVillageCode() {
		return nativeVillageCode;
	}

	public void setNativeVillageCode(Integer nativeVillageCode) {
		this.nativeVillageCode = nativeVillageCode;
	}


}
