package in.gov.cgg.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "ts_vra_options_mst")
public class TsVraPriorityMaster implements Serializable {

	private static final long serialVersionUID = -1293065984784000204L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VRA_PRIORITY_SEQUENCE")
	@SequenceGenerator(name = "VRA_PRIORITY_SEQUENCE", sequenceName = "VRA_PRIORITY_SEQUENCE", allocationSize = 1)
	private Long id;
	
	@Column(name = "hod_id")
	private Integer hodId;
	
	@Column(name = "order_of_priority")
	private Integer orderOfPriority;
	
	@Column(name = "vra_sno")
	private Long vraSno;
	
	@ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({@JoinColumn(name = "hod_id", insertable = false,updatable = false,referencedColumnName="hod_id")})
    private TsHodMaster hodMaster;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getHodId() {
		return hodId;
	}

	public void setHodId(Integer hodId) {
		this.hodId = hodId;
	}

	public Integer getOrderOfPriority() {
		return orderOfPriority;
	}

	public void setOrderOfPriority(Integer orderOfPriority) {
		this.orderOfPriority = orderOfPriority;
	}

	public Long getVraSno() {
		return vraSno;
	}

	public void setVraSno(Long vraSno) {
		this.vraSno = vraSno;
	}

	@Override
	public String toString() {
		return "TsVraPriorityMaster [id=" + id + ", hodId=" + hodId + ", orderOfPriority=" + orderOfPriority
				+ ", vraSno=" + vraSno + "]";
	}

	public TsHodMaster getHodMaster() {
		return hodMaster;
	}

	public void setHodMaster(TsHodMaster hodMaster) {
		this.hodMaster = hodMaster;
	}

	
}
