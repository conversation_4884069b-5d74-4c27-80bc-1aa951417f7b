package in.gov.cgg.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="level_master")
public class LevelMaster {
	@Id
	@Column(name="id")
	private Long id;
	@Column(name="level_id")
	private Long levelid;
	@Column(name="level_name")
	private String levelname;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getLevelid() {
		return levelid;
	}
	public void setLevelid(Long levelid) {
		this.levelid = levelid;
	}
	public String getLevelname() {
		return levelname;
	}
	public void setLevelname(String levelname) {
		this.levelname = levelname;
	}
	@Override
	public String toString() {
		return "LevelMaster [id=" + id + ", levelid=" + levelid + ", levelname=" + levelname + "]";
	}
	
}
