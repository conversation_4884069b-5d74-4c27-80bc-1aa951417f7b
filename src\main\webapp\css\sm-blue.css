.sm-blue {
  background: transparent;
}
.sm-blue a, .sm-blue a:hover, .sm-blue a:focus, .sm-blue a:active {
  padding: 10px 20px;
  /* make room for the toggle button (sub indicator) */
  padding-right: 58px;
  background: #051d4d;
  background-image: linear-gradient(90deg, #659e2d, #659e2d)
  color: #fff;
  font-family: 'Poppins', sans-serif;
  line-height: 23px;
  text-decoration: none;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
}
.sm-blue a.current {
  background: #051d4d;
  background-image: linear-gradient(90deg, #659e2d, #659e2d)
  color: #fff;
}
.sm-blue a.disabled {
  color: #a1d1e8;
}
.sm-blue a .sub-arrow {
  position: absolute;
  top: 50%;
  margin-top: -17px;
  left: auto;
  right: 4px;
  width: 34px;
  height: 34px;
  overflow: hidden;
  font: bold 16px/34px monospace !important;
  text-align: center;
  text-shadow: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.sm-blue a .sub-arrow::before {
  content: '+';
}
.sm-blue a.highlighted .sub-arrow::before {
  content: '-';
}
.sm-blue > li:first-child > a, .sm-blue > li:first-child > :not(ul) a {
  border-radius: 0px;
}
.sm-blue > li:last-child > a, .sm-blue > li:last-child > *:not(ul) a, .sm-blue > li:last-child > ul, .sm-blue > li:last-child > ul > li:last-child > a, .sm-blue > li:last-child > ul > li:last-child > *:not(ul) a, .sm-blue > li:last-child > ul > li:last-child > ul, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul {
  border-radius: 0px;
}
.sm-blue > li:last-child > a.highlighted, .sm-blue > li:last-child > *:not(ul) a.highlighted, .sm-blue > li:last-child > ul > li:last-child > a.highlighted, .sm-blue > li:last-child > ul > li:last-child > *:not(ul) a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > a.highlighted, .sm-blue > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > ul > li:last-child > *:not(ul) a.highlighted {
  border-radius: 0;
}
.sm-blue ul {
  background: #fff;
}
.sm-blue ul ul {
  background: rgba(102, 102, 102, 0.1);
}
.sm-blue ul a, .sm-blue ul a:hover, .sm-blue ul a:focus, .sm-blue ul a:active {
  background: transparent;
  color: #051d4d;
  font-size: 16px;
  text-shadow: none;
  border-left: 8px solid transparent;
}
.sm-blue ul a.current {
  background: #051d4d;;
  background-image: linear-gradient(to bottom, #006188, #006f9c);
  color: #fff;
}
.sm-blue ul a.disabled {
  color: #b3b3b3;
}
.sm-blue ul ul a,
.sm-blue ul ul a:hover,
.sm-blue ul ul a:focus,
.sm-blue ul ul a:active {
  border-left: 16px solid transparent;
}
.sm-blue ul ul ul a,
.sm-blue ul ul ul a:hover,
.sm-blue ul ul ul a:focus,
.sm-blue ul ul ul a:active {
  border-left: 24px solid transparent;
}
.sm-blue ul ul ul ul a,
.sm-blue ul ul ul ul a:hover,
.sm-blue ul ul ul ul a:focus,
.sm-blue ul ul ul ul a:active {
  border-left: 32px solid transparent;
}
.sm-blue ul ul ul ul ul a,
.sm-blue ul ul ul ul ul a:hover,
.sm-blue ul ul ul ul ul a:focus,
.sm-blue ul ul ul ul ul a:active {
  border-left: 40px solid transparent;
}
.sm-blue ul li {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
.sm-blue ul li:first-child {
  border-top: 0;
}

@media (min-width: 768px) {
  /* Switch to desktop layout
  -----------------------------------------------
     These transform the menu tree from
     collapsible to desktop (navbar + dropdowns)
  -----------------------------------------------*/
  /* start... (it's not recommended editing these rules) */
  .sm-blue ul {
    position: absolute;
    width: 12em;
  }

  .sm-blue li {
    float: left;
  }

  .sm-blue.sm-rtl li {
    float: right;
  }

  .sm-blue ul li, .sm-blue.sm-rtl ul li, .sm-blue.sm-vertical li {
    float: none;
  }

  .sm-blue a {
    white-space: nowrap;
  }

  .sm-blue ul a, .sm-blue.sm-vertical a {
    white-space: normal;
  }

  .sm-blue .sm-nowrap > li > a, .sm-blue .sm-nowrap > li > :not(ul) a {
    white-space: nowrap;
  }

  /* ...end */
  .sm-blue {

    border-radius: 0px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }
  .sm-blue a, .sm-blue a:hover, .sm-blue a:focus, .sm-blue a:active, .sm-blue a.highlighted {
    padding: 13px 18px;
    background: transparent;
    color: #fff;
  }
  .sm-blue a:hover, .sm-blue a:focus, .sm-blue a:active, .sm-blue a.highlighted {
    background: #051d4d;
   
  }
  .sm-blue a.current {
    background: #051d4d;
    background-image: linear-gradient(to bottom, #006188, #006f9c);
    color: #fff;
  }
  .sm-blue a.disabled {
    background: #051d4d;
    background-image: linear-gradient(to bottom, #3298c8, #2e8cb8);
    color: #a1d1e8;
  }
  .sm-blue a .sub-arrow {
    top: 22px;
    margin-top: 0;
    bottom: 2px;
    margin-left: -5px;
    right:5px;
    width: 0;
    height: 0;
    border-width: 5px;
    border-style: solid dashed dashed dashed;
    border-color: #ffffff73 transparent transparent transparent;
    background: transparent;
    border-radius: 0;
  }
  .sm-blue a .sub-arrow::before {
    display: none;
  }
  .sm-blue > li:first-child > a, .sm-blue > li:first-child > :not(ul) a {
    border-radius: 0px;
  }
  .sm-blue > li:last-child > a, .sm-blue > li:last-child > :not(ul) a {
    border-radius: 0 !important;
  }
  .sm-blue > li {
    border-left: 1px solid #f5f5f541;
  }
  .sm-blue > li:first-child {
    border-left: 0;
  }
  .sm-blue ul {
    border: 1px solid #a8a8a8;
    padding: 0px 0;
    background: #fff;
    border-radius: 0px !important;
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
  }
  .sm-blue ul ul {
    border-radius: 4px !important;
    background: #fff;
  }
  .sm-blue ul a, .sm-blue ul a:hover, .sm-blue ul a:focus, .sm-blue ul a:active, .sm-blue ul a.highlighted {
    border: 0 !important;
    padding: 9px 23px;
    background: transparent;
    color: #051d4d;
    border-radius: 0 !important;
    border-bottom:thin solid rgba(0,0,0,0.5);
  }
  .sm-blue ul a:hover, .sm-blue ul a:focus, .sm-blue ul a:active, .sm-blue ul a.highlighted {
    background: #051d4d;
    background-image: linear-gradient(to bottom, #051d4d, #051d4d);
    color: #fff;
  }
  .sm-blue ul a.current {
    background: #051d4d;;
    background-image: linear-gradient(to bottom, #006188, #006f9c);
    color: #fff;
  }
  .sm-blue ul a.disabled {
    background: #fff;
    color: #b3b3b3;
  }
  .sm-blue ul a .sub-arrow {
    top: 50%;
    margin-top: -5px;
    bottom: auto;
    left: auto;
    margin-left: 0;
    right: 10px;
    border-style: dashed dashed dashed solid;
    border-color: transparent transparent transparent #a1d1e8;
  }
  .sm-blue ul li {
    border-bottom: thin solid rgba(0,0,0,0.1);
  }
  .sm-blue .scroll-up,
  .sm-blue .scroll-down {
    position: absolute;
    display: none;
    visibility: hidden;
    overflow: hidden;
    background: #fff;
    height: 20px;
  }
  .sm-blue .scroll-up-arrow,
  .sm-blue .scroll-down-arrow {
    position: absolute;
    top: -2px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    overflow: hidden;
    border-width: 8px;
    border-style: dashed dashed solid dashed;
    border-color: transparent transparent #2b82ac transparent;
  }
  .sm-blue .scroll-down-arrow {
    top: 6px;
    border-style: solid dashed dashed dashed;
    border-color: #2b82ac transparent transparent transparent;
  }
  .sm-blue.sm-rtl.sm-vertical a .sub-arrow {
    right: auto;
    left: 10px;
    border-style: dashed solid dashed dashed;
    border-color: transparent #a1d1e8 transparent transparent;
  }
  .sm-blue.sm-rtl > li:first-child > a, .sm-blue.sm-rtl > li:first-child > :not(ul) a {
    border-radius: 0 8px 8px 0;
  }
  .sm-blue.sm-rtl > li:last-child > a, .sm-blue.sm-rtl > li:last-child > :not(ul) a {
    border-radius: 8px 0 0 8px !important;
  }
  .sm-blue.sm-rtl > li:first-child {
    border-left: 1px solid #2b82ac;
  }
  .sm-blue.sm-rtl > li:last-child {
    border-left: 0;
  }
  .sm-blue.sm-rtl ul a .sub-arrow {
    right: auto;
    left: 10px;
    border-style: dashed solid dashed dashed;
    border-color: transparent #a1d1e8 transparent transparent;
  }
  .sm-blue.sm-vertical {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  }
  .sm-blue.sm-vertical a {
    padding: 9px 23px;
  }
  .sm-blue.sm-vertical a .sub-arrow {
    top: 50%;
    margin-top: -5px;
    bottom: auto;
    left: auto;
    margin-left: 0;
    right: 10px;
    border-style: dashed dashed dashed solid;
    border-color: transparent transparent transparent #a1d1e8;
  }
  .sm-blue.sm-vertical > li:first-child > a, .sm-blue.sm-vertical > li:first-child > :not(ul) a {
    border-radius: 8px 8px 0 0;
  }
  .sm-blue.sm-vertical > li:last-child > a, .sm-blue.sm-vertical > li:last-child > :not(ul) a {
    border-radius: 0 0 8px 8px !important;
  }
  .sm-blue.sm-vertical > li {
    border-left: 0 !important;
  }
  .sm-blue.sm-vertical ul {
    border-radius: 4px !important;
  }
  .sm-blue.sm-vertical ul a {
    padding: 9px 23px;
  }
}

/*# sourceMappingURL=sm-blue.css.map */


.main-nav {
  border-radius: 0px;
 }

.main-nav:after {
  clear: both;
  content: "\00a0";
  display: block;
  height: 0;
  font: 0px/0 serif;
  overflow: hidden;
}

.nav-brand {
  float: left;
  margin: 0;
}

.nav-brand a {
  display: block;
  padding: 10px 10px 10px 20px;
  color: #fff;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 22px;
  font-weight: normal;
  line-height: 29px;
  text-decoration: none;
}

#main-menu {
  clear: both;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

@media (min-width: 768px) {
  #main-menu {
    float: left;
    clear: none;
  }
}


/* Mobile menu top separator */

#main-menu:before {
  content: '';
  display: block;
  height: 1px;
  font: 1px/1px sans-serif;
  overflow: hidden;
  background: #f9e4cf52;
}

@media (min-width: 768px) {
  #main-menu:before {
    display: none;
  }

 
}


/* Mobile menu toggle button */

.main-menu-btn {
  float: right;
  margin: 10px;
  position: relative;
  display: inline-block;
  width: 29px;
  height: 29px;
  text-indent: 29px;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}


/* hamburger icon */

.main-menu-btn-icon,
.main-menu-btn-icon:before,
.main-menu-btn-icon:after {
  position: absolute;
  top: 50%;
  left: 2px;
  height: 2px;
  width: 24px;
  background: #fff;
  -webkit-transition: all 0.25s;
  transition: all 0.25s;
}

.main-menu-btn-icon:before {
  content: '';
  top: -7px;
  left: 0;
}

.main-menu-btn-icon:after {
  content: '';
  top: 7px;
  left: 0;
}


/* x icon */

#main-menu-state:checked ~ .main-menu-btn .main-menu-btn-icon {
  height: 0;
  background: transparent;
}

#main-menu-state:checked ~ .main-menu-btn .main-menu-btn-icon:before {
  top: 0;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

#main-menu-state:checked ~ .main-menu-btn .main-menu-btn-icon:after {
  top: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}


/* hide menu state checkbox (keep it visible to screen readers) */

#main-menu-state {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
}


/* hide the menu in mobile view */

#main-menu-state:not(:checked) ~ #main-menu {
  display: none;
}

#main-menu-state:checked ~ #main-menu {
  display: block;
}

@media (min-width: 768px) {
  /* hide the button in desktop view */
  .main-menu-btn {
    position: absolute;
    top: -99999px;
  }
  /* always show the menu in desktop view */
  #main-menu-state:not(:checked) ~ #main-menu {
    display: block;
  }
}

