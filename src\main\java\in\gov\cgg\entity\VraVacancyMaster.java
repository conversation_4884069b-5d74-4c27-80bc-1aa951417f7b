package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.Instant;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.NamedQuery;
import org.springframework.data.annotation.CreatedDate;

@Entity
@Table(name = "vra_vacancy_master")
@NamedQuery(name = "VraVacancyMaster.findAll", query="select vac from VraVacancyMaster vac order by vac.postCategoryName")
public class VraVacancyMaster implements Serializable {

	private static final long serialVersionUID = 2926054188372424755L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VACANCY_SEQUENCE")
	@SequenceGenerator(name = "VACANCY_SEQUENCE", sequenceName = "VACANCY_SEQUENCE", allocationSize = 1)
	private Long sno;
	
	@Column(name = "post_category")
	private String postCategoryName;
	
	@Column(name = "total_vacancies")
	private Long totalVacancies;
	
	@Column(name = "min_qualification_req")
	private String minQualificationReq;
	
	@Column(name = "appointing_auth")
	private String appointingAuth;
	
	@Column(name = "d1")
	private Long d1 = 0l;
	
	@Column(name = "d2")
	private Long d2 = 0l;
	
	@Column(name = "d3")
	private Long d3 = 0l;
	
	@Column(name = "d4")
	private Long d4 = 0l;
	
	@Column(name = "d5")
	private Long d5 = 0l;
	
	@Column(name = "d6")
	private Long d6 = 0l;
	
	@Column(name = "d7")
	private Long d7 = 0l;
	
	@Column(name = "d8")
	private Long d8 = 0l;
	
	@Column(name = "d9")
	private Long d9 = 0l;
	
	@Column(name = "d10")
	private Long d10 = 0l;
	
	@Column(name = "d11")
	private Long d11 = 0l;
	
	@Column(name = "d12")
	private Long d12 = 0l;
	
	@Column(name = "d13")
	private Long d13 = 0l;
	
	@Column(name = "d14")
	private Long d14 = 0l;
	
	@Column(name = "d15")
	private Long d15 = 0l;
	
	@Column(name = "d16")
	private Long d16 = 0l;
	
	@Column(name = "d17")
	private Long d17 = 0l;
	
	@Column(name = "d18")
	private Long d18 = 0l;
	
	@Column(name = "d19")
	private Long d19 = 0l;
	
	@Column(name = "d20")
	private Long d20 = 0l;
	
	@Column(name = "d21")
	private Long d21 = 0l;
	
	@Column(name = "d22")
	private Long d22 = 0l;
	
	@Column(name = "d23")
	private Long d23 = 0l;
	
	@Column(name = "d24")
	private Long d24 = 0l;
	
	@Column(name = "d25")
	private Long d25 = 0l;
	
	@Column(name = "d26")
	private Long d26 = 0l;
	
	@Column(name = "d27")
	private Long d27 = 0l;
	
	@Column(name = "d28")
	private Long d28 = 0l;
	
	@Column(name = "d29")
	private Long d29 = 0l;
	
	@Column(name = "d30")
	private Long d30 = 0l;
	
	@Column(name = "d31")
	private Long d31 = 0l;
	
	@Column(name = "d32")
	private Long d32 = 0l;
	
	@Column(name = "d33")
	private Long d33 = 0l;
	
	@Column(name = "hod_id")
	private Long hodId;
	
	@Column(name = "updated_ip")
	private String createdIp;
	
	@CreatedDate
	@Column(name = "updated_date")
	private Instant createdDate = Instant.now();
	
//	@Transient
//	private Integer[] distVacCount;
	
//	@OneToMany(fetch = FetchType.EAGER)
//    @JoinColumns({@JoinColumn(name = "vac_mst_id", referencedColumnName="id")})
//    private List<VraVacancyDetails> vacDet;


	public String getPostCategoryName() {
		return postCategoryName;
	}

	public void setPostCategoryName(String postCategoryName) {
		this.postCategoryName = postCategoryName;
	}

	public Long getTotalVacancies() {
		return totalVacancies;
	}

	public void setTotalVacancies(Long totalVacancies) {
		this.totalVacancies = totalVacancies;
	}

	public String getMinQualificationReq() {
		return minQualificationReq;
	}

	public void setMinQualificationReq(String minQualificationReq) {
		this.minQualificationReq = minQualificationReq;
	}

	public String getAppointingAuth() {
		return appointingAuth;
	}

	public void setAppointingAuth(String appointingAuth) {
		this.appointingAuth = appointingAuth;
	}

	public Long getD1() {
		return d1;
	}

	public void setD1(Long d1) {
		this.d1 = d1;
	}

	public Long getD2() {
		return d2;
	}

	public void setD2(Long d2) {
		this.d2 = d2;
	}

	public Long getD3() {
		return d3;
	}

	public void setD3(Long d3) {
		this.d3 = d3;
	}

	public Long getD4() {
		return d4;
	}

	public void setD4(Long d4) {
		this.d4 = d4;
	}

	public Long getD5() {
		return d5;
	}

	public void setD5(Long d5) {
		this.d5 = d5;
	}

	public Long getD6() {
		return d6;
	}

	public void setD6(Long d6) {
		this.d6 = d6;
	}

	public Long getD7() {
		return d7;
	}

	public void setD7(Long d7) {
		this.d7 = d7;
	}

	public Long getD8() {
		return d8;
	}

	public void setD8(Long d8) {
		this.d8 = d8;
	}

	public Long getD9() {
		return d9;
	}

	public void setD9(Long d9) {
		this.d9 = d9;
	}

	public Long getD10() {
		return d10;
	}

	public void setD10(Long d10) {
		this.d10 = d10;
	}

	public Long getD11() {
		return d11;
	}

	public void setD11(Long d11) {
		this.d11 = d11;
	}

	public Long getD12() {
		return d12;
	}

	public void setD12(Long d12) {
		this.d12 = d12;
	}

	public Long getD13() {
		return d13;
	}

	public void setD13(Long d13) {
		this.d13 = d13;
	}

	public Long getD14() {
		return d14;
	}

	public void setD14(Long d14) {
		this.d14 = d14;
	}

	public Long getD15() {
		return d15;
	}

	public void setD15(Long d15) {
		this.d15 = d15;
	}

	public Long getD16() {
		return d16;
	}

	public void setD16(Long d16) {
		this.d16 = d16;
	}

	public Long getD17() {
		return d17;
	}

	public void setD17(Long d17) {
		this.d17 = d17;
	}

	public Long getD18() {
		return d18;
	}

	public void setD18(Long d18) {
		this.d18 = d18;
	}

	public Long getD19() {
		return d19;
	}

	public void setD19(Long d19) {
		this.d19 = d19;
	}

	public Long getD20() {
		return d20;
	}

	public void setD20(Long d20) {
		this.d20 = d20;
	}

	public Long getD21() {
		return d21;
	}

	public void setD21(Long d21) {
		this.d21 = d21;
	}

	public Long getD22() {
		return d22;
	}

	public void setD22(Long d22) {
		this.d22 = d22;
	}

	public Long getD23() {
		return d23;
	}

	public void setD23(Long d23) {
		this.d23 = d23;
	}

	public Long getD24() {
		return d24;
	}

	public void setD24(Long d24) {
		this.d24 = d24;
	}

	public Long getD25() {
		return d25;
	}

	public void setD25(Long d25) {
		this.d25 = d25;
	}

	public Long getD26() {
		return d26;
	}

	public void setD26(Long d26) {
		this.d26 = d26;
	}

	public Long getD27() {
		return d27;
	}

	public void setD27(Long d27) {
		this.d27 = d27;
	}

	public Long getD28() {
		return d28;
	}

	public void setD28(Long d28) {
		this.d28 = d28;
	}

	public Long getD29() {
		return d29;
	}

	public void setD29(Long d29) {
		this.d29 = d29;
	}

	public Long getD30() {
		return d30;
	}

	public void setD30(Long d30) {
		this.d30 = d30;
	}

	public Long getD31() {
		return d31;
	}

	public void setD31(Long d31) {
		this.d31 = d31;
	}

	public Long getD32() {
		return d32;
	}

	public void setD32(Long d32) {
		this.d32 = d32;
	}

	public Long getD33() {
		return d33;
	}

	public void setD33(Long d33) {
		this.d33 = d33;
	}

	public Long getHodId() {
		return hodId;
	}

	public void setHodId(Long hodId) {
		this.hodId = hodId;
	}

	public String getCreatedIp() {
		return createdIp;
	}

	public void setCreatedIp(String createdIp) {
		this.createdIp = createdIp;
	}

	public Instant getCreatedDate() {
		return createdDate;
	}

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public void setCreatedDate(Instant createdDate) {
		this.createdDate = createdDate;
	}

//	public Integer[] getDistVacCount() {
//		return distVacCount;
//	}
//
//	public void setDistVacCount(Integer[] distVacCount) {
//		this.distVacCount = distVacCount;
//	}
	
	@Override
	public String toString() {
		return "VraVacancyMaster [sno=" + sno + ", postCategoryName=" + postCategoryName + ", totalVacancies="
				+ totalVacancies + ", minQualificationReq=" + minQualificationReq + ", appointingAuth=" + appointingAuth
				+ ", d1=" + d1 + ", d2=" + d2 + ", d3=" + d3 + ", d4=" + d4 + ", d5=" + d5 + ", d6=" + d6 + ", d7=" + d7
				+ ", d8=" + d8 + ", d9=" + d9 + ", d10=" + d10 + ", d11=" + d11 + ", d12=" + d12 + ", d13=" + d13
				+ ", d14=" + d14 + ", d15=" + d15 + ", d16=" + d16 + ", d17=" + d17 + ", d18=" + d18 + ", d19=" + d19
				+ ", d20=" + d20 + ", d21=" + d21 + ", d22=" + d22 + ", d23=" + d23 + ", d24=" + d24 + ", d25=" + d25
				+ ", d26=" + d26 + ", d27=" + d27 + ", d28=" + d28 + ", d29=" + d29 + ", d30=" + d30 + ", d31=" + d31
				+ ", d32=" + d32 + ", d33=" + d33 + ", hodId=" + hodId + ", createdIp=" + createdIp + ", createdDate="
				+ createdDate + "]";
	}

	
}
