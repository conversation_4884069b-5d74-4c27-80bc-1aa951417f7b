package in.gov.cgg.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import in.gov.cgg.entity.Role;
import in.gov.cgg.repository.RoleRepository;

@Controller
public class roleMasterController {
	@Autowired 
	private RoleRepository rolerepo;
@RequestMapping("/roleMaster")
public String getRoleMaster(Model model) {
	List<Role> roleMasterList=rolerepo.findAll();
	System.out.println(roleMasterList);
	model.addAttribute("rolemaster",roleMasterList);
	return "roleMaster";
}
  
}
