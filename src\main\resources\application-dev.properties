#dev properties

database.driver=org.postgresql.Driver
spring.datasource.url=***************************************************
spring.datasource.username=elecroll_2022_dev
spring.datasource.password=elecroll_2022_dev
spring.main.allow-bean-definition-overriding=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
#database.driver=org.postgresql.Driver
#spring.datasource.url=***************************************
#spring.datasource.username=tsvra
#spring.datasource.password=T$VR@#23

spring.datasource.driver-class-name=org.postgresql.Driver

spring.mvc.view.prefix=/views/
spring.mvc.view.suffix=.jsp

# Show or not log for each sql query
spring.jpa.show-sql=true

# schema will be automatically created afresh for every start of application
spring.jpa.hibernate.ddl-auto=update
server.servlet.session.timeout=60m

#email
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=nowf kqez zmos ayfk
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

undertaking.upload.path=C:/UT_files/uploadedDocs/

#upload paths
FILES_UPLOAD_PATH_OFFICER=/Uploads/IssueTracker/OfficerAttachments/
FILES_UPLOAD_PATH_ADMIN=/Uploads/IssueTracker/AdminAttachments/