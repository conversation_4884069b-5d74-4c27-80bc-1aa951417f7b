deploy_to_dev:
  stage: deploy
  script:
    - 'sh ./mvnw clean tomcat7:deploy -DskipTests=true -Pdev -Dmaven.tomcat.url="$DEV_DEPLOY_URL" -Dtomcat.username="$DEV_DEPLOY_USER" -Dtomcat.password="$DEV_DEPLOY_PASSWORD"'
  tags:
    - lxrunner106
  when: manual
  only:
    - master
  environment:
    name: dev


build_to_Qa:
  stage: deploy
  script:
    - echo "Copying war to QA_sftp"
    - 'sh ./mvnw clean package -DskipTests=true -Pqa'
    - ls -l target
    - export BUILD_TIMESTAMP=`date +%F-%H-%M-%S`
    - export LATEST_DIR=./issueTrackerQA/latest
    - export WAR_FILE="issueTracker.war_$BUILD_TIMESTAMP"
    - lftp -e "mkdir -p $LATEST_DIR;ls -l $LATEST_DIR;put -O $LATEST_DIR target/issueTracker.war -o $WAR_FILE;rm -f $LATEST_DIR/issueTracker-1.0-latest.war;ln -s $WAR_FILE $LATEST_DIR/issueTracker-1.0-latest.war;ls -l $LATEST_DIR;bye" $QA_SITE
    - echo "Copying war to UAT_sftp"
    - 'sh ./mvnw clean package -DskipTests=true -Puat'
    - ls -l target
    - export BUILD_TIMESTAMP=`date +%F-%H-%M-%S`
    - export LATEST_DIR=./issueTrackerUAT/latest
    - export WAR_FILE="issueTracker.war_$BUILD_TIMESTAMP"
    - lftp -e "mkdir -p $LATEST_DIR;ls -l $LATEST_DIR;put -O $LATEST_DIR target/issueTracker.war -o $WAR_FILE;rm -f $LATEST_DIR/issueTracker-1.0-latest.war;ln -s $WAR_FILE $LATEST_DIR/issueTracker-1.0-latest.war;ls -l $LATEST_DIR;bye" $QA_SITE
    - echo "Copying war to PROD_sftp"
    - 'sh ./mvnw clean package -DskipTests=true -Pprod'
    - ls -l target
    - export BUILD_TIMESTAMP=`date +%F-%H-%M-%S`
    - export LATEST_DIR=./issueTrackerPROD/latest
    - export WAR_FILE="issueTracker.war_$BUILD_TIMESTAMP"
    - lftp -e "mkdir -p $LATEST_DIR;ls -l $LATEST_DIR;put -O $LATEST_DIR target/issueTracker.war -o $WAR_FILE;rm -f $LATEST_DIR/issueTracker-1.0-latest.war;ln -s $WAR_FILE $LATEST_DIR/issueTracker-1.0-latest.war;ls -l $LATEST_DIR;bye" $QA_SITE
  tags:
    - lxrunner113
  when: manual
  only:
    - master
  environment:
    name: Qa

deploy_to_Uat:
    stage: deploy
    tags:
    - 104runner    
    when: manual
    environment:
        name: uat
    script:
    - echo 
    - export TC_CONTEXT=issueTracker
    - export TC_WAR=$SFTP_UAT5_UPLOAD/latest/issueTracker-uat-deployed.war
    - echo Deploying $TC_CONTEXT to UAT Tomcat
    - curl -u "$TC_USER_UAT"  "$TC_DEPLOY_UAT/$TC_CONTEXT" -T "$TC_WAR"
    
build_to_Prod:
    stage: deploy
    tags:
    - 104runner    
    when: manual
    environment:
        name: prod
    script:
    - lftp -e "pwd;ls issueTrackerPROD/latest;get issueTrackerPROD/latest/issueTracker-prod-deployed.war;bye"  $UAT_SITE
    - lftp -e "mkdir -p ./issueTrackerPROD/latest;mput -O ./issueTrackerPROD/latest issueTracker-prod-deployed.war;bye" $PROD_SITE
    - echo "copyied issueTracker.war for final build"

    