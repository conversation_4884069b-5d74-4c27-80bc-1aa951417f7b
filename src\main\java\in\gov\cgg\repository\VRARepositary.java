package in.gov.cgg.repository;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.Employee;
import in.gov.cgg.entity.Role;
import in.gov.cgg.entity.TsVraDeatils;

@Repository
public interface VRARepositary extends JpaRepository<TsVraDeatils, Long>{
	

	@Override
	@Query
	public List<TsVraDeatils> findAll();

	TsVraDeatils findBySno(Long sno);

	
	@Query(value="select * from ts_vra_deatils where district_code=:distID order by mandal_name,name ",nativeQuery=true)
    public List<TsVraDeatils> findByDistId(Long distID);
	
	@Query(value="select a.*,c.village_name from ts_vra_deatils a left join ts_village_master c on (a.village_code=c.village_id and a.district_code=c.dist_id) where district_code=:distID and gender is not null order by mandal_name,name ",nativeQuery=true)
    public List<TsVraDeatils> findByDistIdAndgender(Long distID);
	
	@Query(value="select c.village_name,m.mandal_name,a.dist_name,a.name,a.father_name,h.hod_name,v.district_collector_name , e.village_name as native_village,\r\n"
			+ "va.appointing_auth as reporting_officer,a.post_name from ts_vra_deatils a "
			+ "inner join ts_district_master d on (a.district_code=d.dist_code) "
			+ "left join ts_mandal_master1 m on (a.mandal_code=m.mandal_code and a.district_code=m.dist_code and d.dist_code=m.dist_code) "
			+ "left join ts_hod_master h on (a.hod_id=h.hod_id) "
			+ "inner join  vra_district_final_allotment v on (a.district_code=v.district_code) "
			+ "inner join vra_authority_master va on (a.dist_code=va.dist_id and a.hod_id=va.hod_id and a.post_name=va.post_category) \r\n"
			+ "left join ts_village_master c on (a.village_code=c.village_id and a.mandal_code=c.mandal_id and a.district_code=c.dist_id\r\n"
			+ "								 and d.dist_code=c.dist_id and c.mandal_id=m.mandal_code and c.dist_id=m.dist_code) "
			+ "left join ts_village_master e on (a.native_village_code=e.village_id and a.mandal_code=e.mandal_id and a.district_code=e.dist_id\r\n"
			+ "and d.dist_code=c.dist_id and e.mandal_id=m.mandal_code and e.dist_id=m.dist_code)"
			+ "where a.district_code=?1 and a.gender is not null  order by m.mandal_name,a.name ",nativeQuery=true)
    public List<Object[]> findByDistIdAndgenderr(Long distID);

//	@Modifying
//	@Transactional
//	@Query(value="update ts_vra_deatils set gender=:gender,date_of_appointmnet=:dateOfAppointmnet,date_of_birth=:dateOfBirth"
//			+ ",education_qualification=:educationQualification,present_status=:presentStatus,under_taking_document=:underTakingDocument where sno=:sno",nativeQuery=true)
//	public int updateVRA(String gender, Date dateOfAppointmnet, Date dateOfBirth, String educationQualification,
//			String presentStatus, String underTakingDocument, Long sno);
	
	
	@Query(
			  value= "select *,(NO_OF_VRA_RECORDS-NO_OF_VRA_RECORDS_UPDATED) as no_of_pending_to_update from " + 
			  		" ( "+
			  "SELECT user_name,D.dist_name,vacancy_count,COUNT(*) NO_OF_VRA_RECORDS,coalesce(SUM( CASE WHEN gender IS NOT NULL THEN 1 ELSE 0 END),0) NO_OF_VRA_RECORDS_UPDATED,D.dist_code "+
			  		//+ ", '0' as VRA_DATA_CONFIRMED, "+
			  		//+ "'NO' VACANCIES_CONFIRMED \r\n" + 
			  		" FROM \r\n" + 
			  		"(select 1 as distType, coalesce(sum(d1),0) vacancy_count from vra_vacancy_master \r\n" + 
			  		"union all\r\n" + 
			  		"select 2 as distType, coalesce(sum(d2),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		" union all\r\n" + 
			  		"select 3 as distType, coalesce(sum(d3),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 4 as distType, coalesce(sum(d4),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 5 as distType, coalesce(sum(d5),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 6 as distType, coalesce(sum(d6),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 7 as distType, coalesce(sum(d7),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 8 as distType, coalesce(sum(d8),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 9 as distType, coalesce(sum(d9),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 10 as distType, coalesce(sum(d10),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 11 as distType, coalesce(sum(d11),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 12 as distType, coalesce(sum(d12),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 13 as distType, coalesce(sum(d13),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 14 as distType, coalesce(sum(d14),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 15 as distType, coalesce(sum(d15),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 16 as distType, coalesce(sum(d16),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 17 as distType, coalesce(sum(d17),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 18 as distType, coalesce(sum(d18),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 19 as distType, coalesce(sum(d19),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 20 as distType, coalesce(sum(d20),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 21 as distType, coalesce(sum(d21),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 22 as distType, coalesce(sum(d22),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 23 as distType, coalesce(sum(d23),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 24 as distType, coalesce(sum(d24),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 25 as distType, coalesce(sum(d25),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 26 as distType, coalesce(sum(d26),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 27 as distType, coalesce(sum(d27),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 28 as distType, coalesce(sum(d28),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 29 as distType, coalesce(sum(d29),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 30 as distType, coalesce(sum(d30),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 31 as distType, coalesce(sum(d31),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 32 as distType, coalesce(sum(d32),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		"union all\r\n" + 
			  		"select 33 as distType, coalesce(sum(d33),0) as vacancy_count from vra_vacancy_master\r\n" + 
			  		") A JOIN ts_district_master D ON A.distType=D.dist_code\r\n" + 
			  		"JOIN employee C ON C.dist_id=D.dist_code AND A.distType=C.dist_id\r\n" + 
			  		"JOIN ts_vra_deatils VD ON VD.district_code=D.dist_code\r\n" + 
			  		"LEFT JOIN (SELECT LOGIN_FROM FROM users_log GROUP BY LOGIN_FROM) UL ON UL.login_from=C.user_name " + 
			  		" GROUP BY user_name,D.DIST_CODE,A.VACANCY_COUNT,c.id) xy",nativeQuery = true) 
			  public List<Object[]> getVRAStatusDetails();
	
			  
			  @Query(value="SELECT TO_char (current_timestamp, 'dd-mm-yyyy HH24:MI:SS')",nativeQuery = true)
				public String getCurrentTime();

			@Query(value="select count(*) from ts_vra_deatils where hod_id is not null and district_code=:distId",nativeQuery = true)
			public Long checkHodIsNull(Long distId);


			  @Query(value="select count(*) from ts_vra_deatils where district_code=:distID and (gender is null or gender='')",nativeQuery=true)
			public Long getGenderNullDataList(Long distID);


		  @Modifying
		  @Transactional
		  @Query(value="update ts_vra_deatils set under_taking_given=:underTakingGiven where sno=:sno",nativeQuery=true)
		  public Integer updateUTDetails(String underTakingGiven, Long sno);
}
