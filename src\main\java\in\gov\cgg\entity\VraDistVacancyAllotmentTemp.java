package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.Instant;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "vra_dist_vacancy_allotment_temp")
public class VraDistVacancyAllotmentTemp implements Serializable {
	
	private static final long serialVersionUID = -1913859982047647128L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VACANCY_ALLOT_SEQUENCE")
	@SequenceGenerator(name = "VACANCY_ALLOT_SEQUENCE", sequenceName = "VACANCY_ALLOT_SEQUENCE", allocationSize = 1)
	private Long id;
	
	@Column(name = "sno")
	private Long sno;
	
	@Column(name = "post_category")
	private String postCategoryName;
	
	@Column(name = "vacancies")
	private Long vacancies;
	
	@Column(name = "dist_id")
	private Long distId;
	
	@Column(name = "hod_id")
	private Long hodId;
	
	@Column(name = "updated_ip")
	private String createdIp;
	
	@Column(name = "alloted_date")
	private Instant createdDate = Instant.now();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public String getPostCategoryName() {
		return postCategoryName;
	}

	public void setPostCategoryName(String postCategoryName) {
		this.postCategoryName = postCategoryName;
	}

	public Long getDistId() {
		return distId;
	}

	public void setDistId(Long distId) {
		this.distId = distId;
	}

	public String getCreatedIp() {
		return createdIp;
	}

	public void setCreatedIp(String createdIp) {
		this.createdIp = createdIp;
	}

	public Instant getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Instant createdDate) {
		this.createdDate = createdDate;
	}

	public Long getHodId() {
		return hodId;
	}

	public void setHodId(Long hodId) {
		this.hodId = hodId;
	}

	public Long getVacancies() {
		return vacancies;
	}

	public void setVacancies(Long vacancies) {
		this.vacancies = vacancies;
	}

	@Override
	public String toString() {
		return "VraDistVacancyAllotmentTemp [id=" + id + ", sno=" + sno + ", postCategoryName=" + postCategoryName
				+ ", vacancies=" + vacancies + ", distId=" + distId + ", hodId=" + hodId + ", createdIp=" + createdIp
				+ ", createdDate=" + createdDate + "]";
	}

}
