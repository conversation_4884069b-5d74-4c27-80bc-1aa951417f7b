package in.gov.cgg.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import in.gov.cgg.entity.EmployeeLevelMapping;



	
public interface EmployeeLevelMappingRepository extends JpaRepository<EmployeeLevelMapping, Long> {

	public List<EmployeeLevelMapping> findByLevelIdAndProjectTypeId(Long levelId,  Long projectTypeId);
	public List<EmployeeLevelMapping> findByLevelId(Long levelId);

	@Query(value = "select status_name from project_status where status_id=:currentStatus",nativeQuery = true)
	public String findByCurrentStatus(Long currentStatus);
	



	@Query(value="select user_name from employee inner join employee_level_mapping on employee_level_mapping.employee_id=employee.id where level_id=:level",nativeQuery=true)
	public List<Object[]> findemployeelevelnames(Long level);
	
	@Modifying
	@Transactional
	@Query(value="delete from employee_level_mapping where project_type_id=:projectTypeId",nativeQuery=true)
	void deleteByProjectTypeId(Long projectTypeId);	
	

@Modifying
@Transactional
@Query(value="update employee_level_mapping set current_status=:sti where employee_id=:emp1 and level_id=:levelid",nativeQuery=true)
public int updatecurrentstatus(Long emp1,Long sti,Long levelid);
	
@Query(value="select * from employee_level_mapping where employee_id=:empid and project_type_id=:ptype and level_id=:levelid",nativeQuery=true)
public Long findrow(Long empid,Long ptype,Long levelid);
}	


