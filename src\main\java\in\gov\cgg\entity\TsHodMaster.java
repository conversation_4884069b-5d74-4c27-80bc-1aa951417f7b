package in.gov.cgg.entity;

import java.io.Serializable;
import java.util.List;

//import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
//import javax.persistence.JoinTable;
//import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
//import javax.persistence.Table;

import org.hibernate.annotations.NamedNativeQuery;

@Entity
@NamedNativeQuery(name = "TsHodMaster.findAll", query="select hod from ts_hod_master hod order by hod.hod_Name asc")
public class TsHodMaster implements Serializable {

	private static final long serialVersionUID = -4735340076496702442L;

	@Id
	@Column(name = "hod_id")
	private Integer hodId;
	
	@Column(name = "hod_name")
	private String hodName;
	
	@OneToMany(fetch = FetchType.EAGER)
    @JoinColumns({@JoinColumn(name = "hod_id", insertable = false,updatable = false,referencedColumnName="hod_id")})
//	@OToMany(
//		    mappedBy = "hodId",
//		    cascade = CascadeType.ALL,
//		    orphanRemoval = true
//		)
    private List<VraVacancyMaster> vacancyMaster;
//	@ManyToMany
//    @JoinTable(name = "vra_vacancy_master", joinColumns = @JoinColumn(name = "hod_id", referencedColumnName = "hod_id"))
//	private VraVacancyMaster vacancyMaster;

	public Integer getHodId() {
		return hodId;
	}

	public void setHodId(Integer hodId) {
		this.hodId = hodId;
	}

	public String getHodName() {
		return hodName;
	}

	public void setHodName(String hodName) {
		this.hodName = hodName;
	}

	@Override
	public String toString() {
		return "TsHodMaster [hodId=" + hodId + ", hodName=" + hodName + "]";
	}

	public List<VraVacancyMaster> getVacancyMaster() {
		return vacancyMaster;
	}

	public void setVacancyMaster(List<VraVacancyMaster> vacancyMaster) {
		this.vacancyMaster = vacancyMaster;
	}

	
}
