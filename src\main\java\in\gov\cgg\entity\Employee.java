package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.LocalTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

import org.springframework.lang.NonNull;

@Entity
public class Employee implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6869063780085828563L;

	/*
	 * @Id
	 * 
	 * @GeneratedValue(strategy = GenerationType.SEQUENCE, generator =
	 * "USER_SEQUENCE")
	 * 
	 * @SequenceGenerator(name = "USER_SEQUENCE", sequenceName = "USER_SEQUENCE",
	 * allocationSize = 1) private Long id;
	 */
	@Id
	@Column
	@NonNull
	private Long id;

	@Column(unique = true)
	@NonNull
	private String userName;

	@Column
	@NonNull
	private String password;
	
	@Column
	private String emailId;

	@Column
	private Long mobileNumber;

	@Transient
	private String forgotPassword;
	
	
	
	@Column
	private LocalTime lastLoginTime;

	@Column
	private Boolean isActive;
	
	@Column 
	private Long projectTypeId;
	
	@Column 
	private int dist_id;
	 
  
	public Long getId() {
		return id;
	}

	public Long setId(Long id) {
		return this.id = id;
	}

	public String getUserName() {
		return userName;
	}

	public String setUserName(String userName) {
		return this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Long getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(Long mobileNumber) {
		this.mobileNumber = mobileNumber;
	}

	public String getForgotPassword() {
		return forgotPassword;
	}

	public void setForgotPassword(String forgotPassword) {
		this.forgotPassword = forgotPassword;
	}

	public LocalTime getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(LocalTime lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public Boolean getIsActive() {
		return isActive;
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}

	/*public Set<Module> getModule() {
		return module;
	}

	public void setModule(Set<Module> module) {
		this.module = module;
	}*/

	public Long getProjectTypeId() {
		return projectTypeId;
	}

	public void setProjectTypeId(Long projectTypeId) {
		this.projectTypeId = projectTypeId;
	}
	
	
//	@Override
//	public String toString() {
//		return "Employee [id=" + id + ", userName=" + userName + ", password=" + password + ", mobileNumber="
//				+ mobileNumber + ", forgotPassword=" + forgotPassword + ", lastLoginTime=" + lastLoginTime
//				+ ", isActive=" + isActive + ", projectTypeId=" + projectTypeId + "]";
//	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	

	public int getDist_id() {
		return dist_id;
	}

	public void setDist_id(int dist_id) {
		this.dist_id = dist_id;
	}

	@Override
	public String toString() {
		return "Employee [id=" + id + ", userName=" + userName + ", password=" + password + ", emailId=" + emailId
				+ ", mobileNumber=" + mobileNumber + ",  lastLoginTime="
				+ lastLoginTime + ", isActive=" + isActive + ", projectTypeId=" + projectTypeId 
				+ "]";
	}

	

}
