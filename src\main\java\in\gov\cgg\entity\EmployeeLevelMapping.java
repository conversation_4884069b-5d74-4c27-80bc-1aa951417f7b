package in.gov.cgg.entity;

import java.io.Serializable;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.SequenceGenerator;

import org.springframework.lang.NonNull;

@Entity
public class EmployeeLevelMapping implements Serializable {

	
	private static final long serialVersionUID = 7250270601512937908L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "Mapping")
	@SequenceGenerator(name = "Mapping", sequenceName = "Mapping", allocationSize = 1)
	private Long id;

	@Column(name="level_id",unique = true)
	@NonNull
	private Long levelId;
	@Column(name="role_id")
	private Long roleId;
	@Column(name="project_type_id")
	private Long projectTypeId;
	@Column(name="employee_id")
	private Long employeeId;
	@Column(name="current_status")
	private Long currentStatus;
		
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getLevelId() {
		return levelId;
	}

	public void setLevelId(Long levelId) {
		this.levelId = levelId;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getProjectTypeId() {
		return projectTypeId;
	}

	public void setProjectTypeId(Long projectTypeId) {
		this.projectTypeId = projectTypeId;
	}

	public Long getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}

	public Long getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(Long currentStatus) {
		this.currentStatus = currentStatus;
	}

	@Override
	public String toString() {
		return "EmployeeLevelMapping [id=" + id + ", levelId=" + levelId + ", roleId=" + roleId + ", projectTypeId="
				+ projectTypeId + ", employeeId=" + employeeId + ", currentStatus=" + currentStatus + "]";
	}
	
	

}