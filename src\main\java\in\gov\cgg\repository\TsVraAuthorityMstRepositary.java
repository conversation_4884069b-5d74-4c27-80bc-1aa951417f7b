package in.gov.cgg.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.TsVraAuthorityMaster;

@Repository
public interface TsVraAuthorityMstRepositary extends JpaRepository<TsVraAuthorityMaster, Long>{
	
	@Modifying
	@Transactional
	@Query(value="delete from vra_authority_master where dist_id = ?1",nativeQuery=true)
	Integer deleteByDistId(Long distId);

	List<TsVraAuthorityMaster> findByDistId(Long dist_id);
}
