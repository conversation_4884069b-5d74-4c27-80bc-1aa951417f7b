package in.gov.cgg.entity;

import java.io.Serializable;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;


@Entity
public class Level implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4779022173068768155L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE,generator = "level_sequence")
	@SequenceGenerator(name = "level_sequence", sequenceName = "SEQUENCE_level", allocationSize=1)
	private Long id;
	
	@Column
	private Long levelOrder;
	
	@Column
	private String name;
	
	@ManyToOne
	@JoinColumn(name = "employee_id")
	private Employee employee;
	
//	@OneToMany(mappedBy = "level")
//	@JsonIgnore
//	private List<Task> task;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getLevelOrder() {
		return levelOrder;
	}

	public void setLevelOrder(Long levelOrder) {
		this.levelOrder = levelOrder;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	

	@Override
	public String toString() {
		return "Level [id=" + id + ", levelOrder=" + levelOrder + ", name=" + name + "]";
	}

}
