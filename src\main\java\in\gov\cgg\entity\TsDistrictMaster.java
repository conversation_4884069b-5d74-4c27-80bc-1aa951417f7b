package in.gov.cgg.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.NamedQuery;

@Entity
@Table(name = "ts_district_master")
@NamedQuery(name = "TsDistrictMaster.findAll", query="select dist from TsDistrictMaster dist order by dist.distCode")
public class TsDistrictMaster {
	
	@Id
	@Column(name = "dist_code")
	private Integer distCode;
	
	@Column(name = "dist_name")
	private String distName;

	public Integer getDistCode() {
		return distCode;
	}

	public void setDistCode(Integer distCode) {
		this.distCode = distCode;
	}

	public String getDistName() {
		return distName;
	}

	public void setDistName(String distName) {
		this.distName = distName;
	}

	@Override
	public String toString() {
		return "TsDistrictMaster [distCode=" + distCode + ", distName=" + distName + "]";
	}
}
