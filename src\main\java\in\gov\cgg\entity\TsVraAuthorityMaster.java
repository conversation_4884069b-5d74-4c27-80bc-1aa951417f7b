package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.data.annotation.CreatedDate;

@Entity
@Table(name="vra_authority_master")
public class TsVraAuthorityMaster  implements Serializable {

	private static final long serialVersionUID = 1555669710048493193L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VRA_AUTHORITY_SEQUENCE")
	@SequenceGenerator(name = "VRA_AUTHORITY_SEQUENCE", sequenceName = "VRA_AUTHORITY_SEQUENCE", allocationSize = 1)
	private Long sno;
	
	@Column(name = "dist_id")
	private Long distId;
	
	@Column(name = "hod_id")
	private Long hodId;
	
	@Column(name = "vac_post_cat_sno")
	private Long vacPostCatId;
	
	@Column(name = "post_category")
	private String postCategoryName;
	
	@Column(name = "min_qualification_req")
	private String minQualificationReq;
	
	@Column(name = "appointing_auth")
	private String appointingAuth;
	
	@CreatedDate
	@Column(name = "updated_date")
	private Instant updatedDate = Instant.now();
	
	@Transient
	private Long vacancies;
	
	@Transient
	private String hodName;
	
	@Transient
	List<TsVraAuthorityMaster> vacDetailsList =  new ArrayList<TsVraAuthorityMaster>();
	

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public Long getDistId() {
		return distId;
	}

	public void setDistId(Long distId) {
		this.distId = distId;
	}

	public Long getHodId() {
		return hodId;
	}

	public void setHodId(Long hodId) {
		this.hodId = hodId;
	}

	public String getPostCategoryName() {
		return postCategoryName;
	}

	public void setPostCategoryName(String postCategoryName) {
		this.postCategoryName = postCategoryName;
	}

	public String getMinQualificationReq() {
		return minQualificationReq;
	}

	public void setMinQualificationReq(String minQualificationReq) {
		this.minQualificationReq = minQualificationReq;
	}

	public String getAppointingAuth() {
		return appointingAuth;
	}

	public void setAppointingAuth(String appointingAuth) {
		this.appointingAuth = appointingAuth;
	}

	public Instant getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Instant updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Long getVacancies() {
		return vacancies;
	}

	public void setVacancies(Long vacancies) {
		this.vacancies = vacancies;
	}

	public Long getVacPostCatId() {
		return vacPostCatId;
	}

	public void setVacPostCatId(Long vacPostCatId) {
		this.vacPostCatId = vacPostCatId;
	}

	@Override
	public String toString() {
		return "TsVraAuthorityMaster [sno=" + sno + ", distId=" + distId + ", hodId=" + hodId + ", vacPostCatId="
				+ vacPostCatId + ", postCategoryName=" + postCategoryName + ", minQualificationReq="
				+ minQualificationReq + ", appointingAuth=" + appointingAuth + ", updatedDate=" + updatedDate + "]";
	}

	public String getHodName() {
		return hodName;
	}

	public void setHodName(String hodName) {
		this.hodName = hodName;
	}

	public List<TsVraAuthorityMaster> getVacDetailsList() {
		return vacDetailsList;
	}

	public void setVacDetailsList(List<TsVraAuthorityMaster> vacDetailsList) {
		this.vacDetailsList = vacDetailsList;
	}
}
