package in.gov.cgg.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ts_village_master")
public class TsVillageMaster {

	@Id
	@Column(name = "village_id")
	private Integer villageId;
	
	@Column(name = "dist_id")
	private Integer distId;
	
	@Column(name = "div_id")
	private Integer divId;
	
	@Column(name = "mandal_id")
	private Integer mandalId;
	
	@Column(name = "village_name")
	private String villageName;


	public Integer getDistId() {
		return distId;
	}

	public void setDistId(Integer distId) {
		this.distId = distId;
	}

	public Integer getDivId() {
		return divId;
	}

	public void setDivId(Integer divId) {
		this.divId = divId;
	}

	public Integer getMandalId() {
		return mandalId;
	}

	public void setMandalId(Integer mandalId) {
		this.mandalId = mandalId;
	}

	public String getVillageName() {
		return villageName;
	}

	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}

	public Integer getVillageId() {
		return villageId;
	}

	public void setVillageId(Integer villageId) {
		this.villageId = villageId;
	}

	@Override
	public String toString() {
		return "TsVillageMaster [villageId=" + villageId + ", distId=" + distId + ", divId=" + divId + ", mandalId="
				+ mandalId + ", villageName=" + villageName + "]";
	}

}
