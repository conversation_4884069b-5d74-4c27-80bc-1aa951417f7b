# Issue Tracker Application

A comprehensive Java Spring Boot web application for tracking and managing technical and operational issues within government organizations. This system provides role-based access control, issue lifecycle management, email notifications, and detailed reporting capabilities.

## 🚀 Project Overview

The Issue Tracker Application is designed to streamline issue management processes for government departments. It allows users to:

- **Raise Issues**: Submit technical and operational issues with detailed descriptions, attachments, and priority levels
- **Track Progress**: Monitor issue status through various stages (Open, In Progress, Resolved, Closed)
- **Role-based Management**: Different access levels for officers, administrators, and approvers
- **Email Notifications**: Automated email alerts for issue assignments and status updates
- **Comprehensive Reporting**: Generate detailed reports and analytics on issue trends
- **File Management**: Upload and manage attachments for issues
- **Hierarchical Organization**: Support for modules, sub-modules, and services organization

## 🛠️ Technology Stack

### Backend
- **Java**: 1.8
- **Spring Boot**: 2.4.1
- **Spring Security**: Authentication and authorization
- **Spring Data JPA**: Database operations
- **Hibernate**: ORM framework
- **Maven**: Dependency management and build tool

### Database
- **PostgreSQL**: Primary database
- **Hibernate Validator**: Data validation

### Frontend
- **JSP**: Server-side rendering
- **Apache Tiles**: Layout management
- **Bootstrap**: UI framework
- **JavaScript/jQuery**: Client-side interactions
- **DataTables**: Advanced table features
- **SweetAlert**: User-friendly alerts
- **Toastr**: Notification system

### Additional Libraries
- **iText PDF**: PDF generation and manipulation
- **Lombok**: Reducing boilerplate code
- **Commons IO**: File operations
- **JavaMail**: Email functionality

## 📁 Project Structure

```
src/
├── main/
│   ├── java/in/gov/cgg/
│   │   ├── TsVraApplication.java          # Main Spring Boot application
│   │   ├── ServletInitializer.java       # WAR deployment configuration
│   │   ├── config/                       # Configuration classes
│   │   │   ├── SecurityConfiguration.java # Spring Security setup
│   │   │   ├── TilesConfig.java          # Apache Tiles configuration
│   │   │   └── UserPrincipal.java        # Custom user principal
│   │   ├── controller/                   # REST and MVC controllers
│   │   │   ├── IssueTrackerReportController.java # Issue reporting endpoints
│   │   │   ├── RaiseIssueController.java # Issue creation and management
│   │   │   ├── HomeController.java       # Home and user management
│   │   │   ├── EmailController.java      # Email operations
│   │   │   └── FilePreviewAndDownloadController.java # File operations
│   │   ├── entity/                       # JPA entities
│   │   │   ├── IssueForm.java           # Main issue entity
│   │   │   ├── IssueStatus.java         # Issue status tracking
│   │   │   ├── IssueTrackerUser.java    # User entity
│   │   │   ├── IssueTrackerModule.java  # Module entity
│   │   │   ├── IssueTrackerSubmodule.java # Sub-module entity
│   │   │   ├── IssueTrackerService.java # Service entity
│   │   │   └── Role.java                # User roles
│   │   ├── repository/                  # Data access layer
│   │   ├── service/                     # Business logic layer
│   │   ├── dto/                         # Data transfer objects
│   │   ├── util/                        # Utility classes
│   │   ├── exception/                   # Custom exceptions
│   │   ├── filters/                     # Security filters
│   │   └── advice/                      # Global advice
│   ├── resources/
│   │   ├── application.properties       # Main configuration
│   │   ├── application-{env}.properties # Environment-specific configs
│   └── webapp/                          # Web resources
│       ├── views/                       # JSP templates
│       ├── css/                         # Stylesheets
│       ├── js/                          # JavaScript files
│       ├── images/                      # Static images
│       └── WEB-INF/                     # Web configuration
└── test/                                # Test classes
```

## 📋 Prerequisites

Before running the application, ensure you have the following installed:

- **Java Development Kit (JDK)**: Version 1.8 or higher
- **Apache Maven**: Version 3.6+ for dependency management
- **PostgreSQL**: Version 10+ for database
- **Apache Tomcat**: Version 9+ (for WAR deployment)
- **Git**: For version control

## ⚙️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd issuetracker
```

### 2. Database Setup
1. Install PostgreSQL and create a database
2. Update database configuration in `application-{env}.properties`
3. The application will automatically create tables on first run (DDL auto-update enabled)

### 3. Configure Application Properties
Update the following files based on your environment:

**application-dev.properties** (Development):
```properties
spring.datasource.url=**********************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# Email configuration
spring.mail.host=smtp.gmail.com
spring.mail.username=<EMAIL>
spring.mail.password=your_app_password

# File upload paths
FILES_UPLOAD_PATH_OFFICER=/path/to/officer/attachments/
FILES_UPLOAD_PATH_ADMIN=/path/to/admin/attachments/
```

### 4. Build the Application
```bash
mvn clean compile
```

### 5. Run Tests
```bash
mvn test
```

## 🚀 Running the Application

### Development Mode
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Production Build
```bash
mvn clean package -Pprod
```

### Deploy WAR file
```bash
mvn tomcat7:deploy -Pdev
```

The application will be available at:
- **Development**: `http://localhost:8081/IssueTracker`
- **Production**: `http://your-server:port/issueTracker`

## 🔐 Default Access

The application uses role-based authentication. Contact your system administrator for user credentials.

**Role Types:**
- **Operational Approver** (Role ID: 1): Operational issue management  
- **Technical Approver** (Role ID: 999): Technical issue management
- **District Officer** (Role ID: 2): District-level issue reporting
- **SOPR** (Role ID: 25): Special operational reporting

## 📚 API Documentation

### Main Endpoints

#### Issue Management
- `GET /` - Home dashboard
- `GET /raiseIssue` - Issue creation form
- `POST /submitIssue` - Submit new issue
- `POST /ticketDetails` - Get issue details
- `GET /issueTrackerReport` - Issue reports dashboard

#### Reports and Analytics
- `GET /technicalReport` - Technical issues report
- `GET /operationalReport` - Operational issues report  
- `POST /mainReport` - Main reporting interface
- `POST /getInfo` - Get detailed issue information

#### User Management
- `GET /registerEmployee` - Employee registration
- `POST /registerEmployee` - Create/update employee
- `GET /editEmployeeDetails` - Edit employee details
- `POST /deleteEmpDetails` - Delete employee

#### File Operations
- `GET /preview/{fileName}` - Preview uploaded files
- File upload endpoints for attachments

#### Authentication
- `GET /login` - Login page
- `POST /authenticate` - Authentication processing
- `GET /logout` - Logout

## 🗄️ Database Schema

### Core Entities

**issue_tracker_master2** - Main issues table
- `id` (Primary Key)
- `issue_tracker_id` (Unique ticket ID)
- `raised_by_officer_id` (User who raised the issue)
- `module_id`, `sub_module_id`, `service_id` (Hierarchical organization)
- `issue_type` (Technical/Operational)
- `issue_description`, `priority`, `status`
- `file_attachment_*` (File management fields)
- Audit fields (created_by, created_date, etc.)

**issue_tracker_status_table** - Issue status tracking
- `id` (Primary Key)
- `issue_ticket_id` (Foreign key to issues)
- `status`, `action_by`, `action_time`
- `remarks`, `file_path`, `assigned_to`

**users** - User management
- `sno` (Primary Key)
- `userid`, `password`, `uname`, `lname`
- `designation`, `phone`, `email`
- `district_id`, `mandal_id` (Geographic assignment)
- `role_id` (Role-based access)

**Hierarchical Structure:**
- `issue_tracker_module` → `issue_tracker_submodule` → `issue_tracker_service`
- `issue_tracker_roles_mst` - Role definitions
- `issue_tracker_approver_details` - Approval workflow

## 🧪 Testing

### Run Unit Tests
```bash
mvn test
```

### Run Integration Tests
```bash
mvn verify
```

### Test Coverage
```bash
mvn jacoco:report
```

The application includes:
- Unit tests for service layer
- Integration tests for controllers
- Database integration tests

## 🚀 Deployment

### Environment Profiles

The application supports multiple deployment environments:

#### Development
```bash
mvn clean package -Pdev
```

#### QA/Testing
```bash
mvn clean package -Pqa
```

#### UAT
```bash
mvn clean package -Puat
```

#### Production
```bash
mvn clean package -Pprod
```

### Deployment Steps

1. **Build the WAR file**:
   ```bash
   mvn clean package -P{environment}
   ```

2. **Deploy to Tomcat**:
   ```bash
   mvn tomcat7:deploy -P{environment}
   ```

3. **Environment Variables**:
   Set the following environment variables:
   - `maven.tomcat.url` - Tomcat manager URL
   - `tomcat.username` - Tomcat manager username
   - `tomcat.password` - Tomcat manager password

### Docker Deployment (Optional)
```dockerfile
FROM tomcat:9-jdk8
COPY target/issueTracker.war /usr/local/tomcat/webapps/
EXPOSE 8080
```

## 🤝 Contributing

### Development Guidelines

1. **Code Style**: Follow Java coding conventions
2. **Testing**: Write unit tests for new features
3. **Documentation**: Update README for significant changes
4. **Security**: Follow secure coding practices

### Contribution Process

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Review Checklist

- [ ] Code follows project conventions
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed

## 📄 License

This project is licensed under the [Government of Telangana] License. See the LICENSE file for details.

## 📞 Support

For technical support or questions:

- **Email**: <EMAIL>
- **Documentation**: Internal government documentation
- **Issue Tracking**: Use the application's built-in issue tracking system

## 🔄 Version History

- **v0.0.1-SNAPSHOT**: Initial development version
- Features: Basic issue tracking, user management, reporting

---

**Developed by**: Government of Telangana - CGG Department  
**Maintained by**: TSEC Development Team
