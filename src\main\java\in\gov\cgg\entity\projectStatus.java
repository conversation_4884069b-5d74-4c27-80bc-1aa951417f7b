package in.gov.cgg.entity;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name="project_status")
public class projectStatus {
@Id
@Column(name="status_id")
private Long statusid;

@Column(name="status_name")
private String statusname;



public Long getStatusid() {
	return statusid;
}

public void setStatusid(Long statusid) {
	this.statusid = statusid;
}

public String getStatusname() {
	return statusname;
}

public void setStatusname(String statusname) {
	this.statusname = statusname;
}



@Override
public String toString() {
	return "projectStatus [statusid=" + statusid + ", statusname=" + statusname + "]";
}

}
