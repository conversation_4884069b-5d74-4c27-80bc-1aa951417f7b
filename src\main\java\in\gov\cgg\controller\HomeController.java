package in.gov.cgg.controller;

import in.gov.cgg.config.UserPrincipal;
import in.gov.cgg.dto.levelemployee;
import in.gov.cgg.entity.Employe_level;
import in.gov.cgg.entity.Employee;
import in.gov.cgg.entity.EmployeeLevelMapping;
import in.gov.cgg.entity.IssueForm;
import in.gov.cgg.entity.LevelMaster;
import in.gov.cgg.entity.Role;
import in.gov.cgg.entity.TsMandalMaster;
import in.gov.cgg.entity.UsersLog;
import in.gov.cgg.entity.VraVacancyMaster;
import in.gov.cgg.entity.projectStatus;
import in.gov.cgg.repository.Connjdbc;
import in.gov.cgg.repository.EmployeeLevelMappingRepository;
import in.gov.cgg.repository.LevelMasterRepositary;
import in.gov.cgg.repository.Project_Status;
import in.gov.cgg.repository.RoleRepository;
import in.gov.cgg.repository.UserRepository;
import in.gov.cgg.repository.UsersLogRepositary;
import in.gov.cgg.util.otpGenerator;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
public class HomeController {

  public Connection con, conn, connection;
  public Statement st;
  public PreparedStatement pst;

  @Value("${spring.datasource.driver-class-name}")
  public String databasedriverclassNname;

  @Value("${spring.datasource.url}")
  public String databaseurl;

  @Value("${spring.datasource.username}")
  public String databaseusername;

  @Value("${spring.datasource.password}")
  public String databasepassword;

  @Autowired
  private RoleRepository rolerepo;

  @Autowired
  private UserRepository userrepo;

  @Autowired
  private EmployeeLevelMappingRepository emp;

  @Autowired
  private UsersLogRepositary usersLogRepo;

  @Autowired
  private LevelMasterRepositary level;

  @Autowired
  private Project_Status ps;

  @Autowired
  private otpGenerator otp;

  private static Logger logger = LoggerFactory.getLogger(HomeController.class);

  @GetMapping("/")
  public String homePage(Model model, HttpServletRequest request) {
    String username = "";
    Authentication authentication = SecurityContextHolder
      .getContext()
      .getAuthentication();
    String roleid = "";
    if (authentication != null && authentication.isAuthenticated()) {
      Object principal = authentication.getPrincipal();

      if (principal instanceof UserDetails) {
        UserPrincipal user = (UserPrincipal) principal;
        username = user.getUsername();
        request.getSession().setAttribute("AuthenticatedUser", username);
        request.getSession().setAttribute("roleId", user.getUser().getRoleId());
        if (user.getUser().getRoleId() != null) {
          roleid = user.getUser().getRoleId().toString();
        }
      }
    } else {
      return "redirect:/login";
    }
    model.addAttribute("issue", new IssueForm());
    if (roleid.equals("1") || roleid.equals("999")) {
      return "redirect:/issueTrackerApproval";
    }
    return "raiseIssue";
  }

  @GetMapping("/changePassword")
  public String changePassword(Model model, Authentication authentication) {
    UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
    model.addAttribute("userid", user.getUser().getUserId());
    return "changePassword";
  }

  @PostMapping("/updateChangePassword")
  public String updateChangePassword(
    @RequestParam("password") String password,
    @RequestParam("id") Long id,
    Model model,
    RedirectAttributes redirectAttributes,
    Authentication authentication,
    HttpServletRequest request
  ) {
    try {
      Integer updated = userrepo.updateNewPassword(password, id);
      if (updated > 0) {
        userrepo.updateIsActive(id);
        redirectAttributes.addFlashAttribute(
          "updated",
          "Password Changed succesfully.<a href='/login'>re-login</a>"
        );
      } else {
        redirectAttributes.addFlashAttribute(
          "failuremsg",
          "Please check the feilds! Password was not changed"
        );
      }
    } catch (Exception e) {
      e.printStackTrace();
      redirectAttributes.addFlashAttribute(
        "failuremsg",
        "Please check the feilds! Password was not changed"
      );
      return "redirect:/changePassword";
    }
    return "redirect:/changePassword";
  }

  @RequestMapping(value = "checkPassword")
  public @ResponseBody Boolean checkPassword(
    @RequestParam("password") String password,
    @RequestParam("id") Long id
  ) {
    String existingPass = userrepo.findPasswordDetails(id);
    Boolean status = true;
    if (!password.equalsIgnoreCase(existingPass)) {
      status = false;
    }
    return status;
  }

  @GetMapping("/login")
  public String loginPage(Model model) {
    logger.info("In loginPage >>>>>>>>>>>>>>>");
    model.addAttribute("logoutMessage", model.getAttribute("logoutMessage"));
    return "login";
  }

  @PostMapping("/login")
  public String login(
    Authentication authentication,
    HttpServletRequest request
  ) {
    logger.info("In loginPage Post >>>>>>>>>>>>>>>" + authentication.getName());
    return "index";
  }

  @GetMapping("/afterLogout")
  public String logoutUrlIs(RedirectAttributes redirectAttributes) {
    logger.info("In Logut URL >>>>>>>>>>>>>>>");
    System.err.println(
      ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
    );
    redirectAttributes.addFlashAttribute(
      "logoutMessage",
      "You have been succefully logged out"
    );
    return "redirect:login";
  }

  /*@GetMapping("/loginFailure")
	public String errorPage(Model model) {
		System.out.println("In Error");
		model.addAttribute("error",true);
		model.addAttribute("loginFailed","Invalid UserName or Password");
		return "login";
	}*/

  @RequestMapping(value = "/forgotPassword", method = RequestMethod.GET)
  public String index(Model model, @ModelAttribute Employee employee) {
    if (employee.getUserName() == null) {
      model.addAttribute("view_page", "main_page");
      System.out.println("null");
    }
    System.out.println("all");

    return "forgotPassword";
  }

  @RequestMapping(value = "/forgotPassword", method = RequestMethod.POST)
  public String sendingOtptoUser(
    Model model,
    @ModelAttribute Employee employee,
    RedirectAttributes redirectAttributes
  ) {
    int otpNumber = 0;
    int fetchDetails = 0;
    try {
      fetchDetails = userrepo.finduserName(employee.getUserName());
      System.out.println(fetchDetails);
      if (fetchDetails != 0) {
        Employee emp = userrepo.finduser(employee.getUserName());
        System.out.println(emp.getMobileNumber());
        otpNumber = otp.generateRandomNumber();
        model.addAttribute("otpnumber", otpNumber);
        model.addAttribute("empId", emp.getId());
        model.addAttribute("empName", emp.getUserName());
        model.addAttribute("otp", "otp_page");
      }
    } catch (Exception e) {
      e.printStackTrace();
      redirectAttributes.addFlashAttribute("userfailmsg", "In valid User Name");
      return "redirect:forgotPassword";
    }
    return "forgotPassword";
  }

  @RequestMapping(value = "/validateotp", method = RequestMethod.POST)
  public String resetPassword(Model model, @ModelAttribute Employee employee) {
    model.addAttribute("EmpName", employee.getUserName());
    model.addAttribute("EmpId", employee.getId());
    model.addAttribute("reset", "reset_page");

    return "forgotPassword";
  }

  @RequestMapping(value = "/confirmPassword", method = RequestMethod.POST)
  public String resetNewPassword(
    Model model,
    @ModelAttribute Employee employee,
    RedirectAttributes redirectAttributes
  ) {
    Long empId = employee.getId();
    int confirmpassword = userrepo.updateNewPassword(
      employee.getPassword(),
      empId
    );
    System.out.println("new password update successfully" + confirmpassword);
    redirectAttributes.addFlashAttribute(
      "successmsg",
      "" +
      employee.getUserName() +
      " your password is successfully Updated,now you can Log-In"
    );
    return "redirect:login";
  }

 

  

  @PostMapping(value = "registerEmployee")
  public String InsertEmployees(
    Model model,
    @ModelAttribute(value = "employee") Employee employee,
    @RequestParam("actionVal") String actionVal,
    BindingResult result,
    RedirectAttributes redirectAttributes
  ) {
    try {
      employee.setIsActive(true);
      Employee emp = userrepo.save(employee);
      if (emp != null) {
        redirectAttributes.addFlashAttribute(
          "successmsg",
          "Employee(" +
          employee.getUserName() +
          ") has been " +
          actionVal +
          " successfully "
        );
      } else {
        redirectAttributes.addFlashAttribute(
          "failmsg",
          "Something went wrong..! Please check the fields"
        );
      }
    } catch (Exception e) {
      e.printStackTrace();
      redirectAttributes.addFlashAttribute(
        "failmsg",
        "Something went wrong..! Please check the fields"
      );
      return "redirect:registerEmployee";
    }
    return "redirect:registerEmployee";
  }

  @RequestMapping("/deleteEmpDetails")
  public String deleteEmpDetails(
    Model m,
    @RequestParam("id") Long id,
    @RequestParam("userName") String userName,
    RedirectAttributes redirectAttributes
  ) {
    try {
      userrepo.deleteById(id);
      redirectAttributes.addFlashAttribute(
        "successmsg",
        "Employee(" + userName + ") Details has been Deleted successfully "
      );
    } catch (Exception e) {
      e.printStackTrace();
      redirectAttributes.addFlashAttribute(
        "failmsg",
        "Unable to delete the record"
      );
      return "redirect:registerEmployee";
    }
    return "redirect:registerEmployee";
  }

  @RequestMapping("/editEmployeeDetails")
  public ModelAndView editEmployeeDetails(
    ModelMap model,
    @RequestParam("id") Long id
  ) {
    ModelAndView mav = new ModelAndView();
    try {
      Employee employee = userrepo.findAllById(id);
      model.addAttribute("employee", employee);
      
      mav.setViewName("Registration");
    } catch (Exception e) {
      e.printStackTrace();
    }
    return mav;
  }

  @RequestMapping("/Employeelevel")
  public String taskLevel(Model m, Authentication authentication) {
    //	UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
    //m.addAttribute("projects", projectRepository.findByEmployee(user.getEmployee()));
    List<Role> list = rolerepo.findAll();
    m.addAttribute("list", list);
    return "elm";
  }

  @PostMapping("/getemp")
  @ResponseBody
  public List<Employee> getempdetails(
    @RequestParam Long projectType,
    Employee Employee
  ) {
    System.out.println("hello");
    //List<String> a=userrepo.finduserNameByProjectTypeId(projectType);
    List<Employee> listt = userrepo.findAll();
    for (Employee ob : listt) {
      System.out.println(ob);
    }
    return listt;
  }

  @PostMapping("/EmployeeLevelMap")
  public String InsertEmployeeLevelMapping1(
    Employe_level Employ,
    RedirectAttributes redirectAttributes
  ) {
    emp.deleteByProjectTypeId(Employ.getProjecttypeid());
    //		System.out.println("isDeleted "+status);

    Long i = (long) Employ.getEmpid().size();
    System.out.println("i-->" + i);
    for (int x = 0; x < i; x++) {
      System.out.println(Employ.getEmpid().get(x));
      System.out.println(Employ.getStatus().get(x));
      System.out.println(Employ.getLevelid().get(x));
      Long empid = Employ.getEmpid().get(x);
      Long stid = Employ.getStatus().get(x);
      Long levelID = Employ.getLevelid().get(x);
      Long projectid = Employ.getProjecttypeid();
      if (
        empid != null && Employ.getProjecttypeid() != null && levelID != null
      ) {
        Long there = emp.findrow(empid, Employ.getProjecttypeid(), levelID);
        System.out.println("there" + there);
        if (there != null) {
          System.out.println("if");
          int h = emp.updatecurrentstatus(
            empid,
            stid,
            Employ.getLevelid().get(x)
          );
          System.out.println("hhhh" + h);
        } else {
          EmployeeLevelMapping e1 = new EmployeeLevelMapping();
          e1.setEmployeeId(empid);
          e1.setLevelId(levelID);
          e1.setProjectTypeId(projectid);
          Long roleid = userrepo.findroleid(Employ.getEmpid().get(x));
          e1.setRoleId(roleid);
          e1.setCurrentStatus(stid);
          emp.save(e1);
          System.out.println(e1);
        }
      }
    }

    System.out.println("level" + Employ.getLevelid());
    System.out.println("employe--s" + Employ.getEmpid().size());
    System.out.println("employe---" + Employ.getEmpid());
    System.out.println("status----" + Employ.getStatus());
    redirectAttributes.addFlashAttribute(
      "successmsg",
      "Task has been Added successfully "
    );
    return "redirect:Employeelevel";
  }

  @RequestMapping("/le")
  @ResponseBody
  public List<LevelMaster> leve(@RequestParam Long projectType) {
    List<LevelMaster> list = level.findAll();

    for (LevelMaster a : list) {
      System.out.println(a.getLevelid());
    }

    return list;
  }

  @RequestMapping("/status")
  @ResponseBody
  public List<projectStatus> taskstatus() {
    List<projectStatus> slist = ps.findAll();
    return slist;
  }

  @RequestMapping("/ww")
  @ResponseBody
  public List<levelemployee> employeeListBylevel(
    @RequestParam int prtype,
    @RequestParam int levelid
  ) {
    String sname = null;
    List<levelemployee> a = new ArrayList<levelemployee>();
    try {
      connection =
        Connjdbc.getConnection(
          databasedriverclassNname,
          databaseurl,
          databaseusername,
          databasepassword
        );
      st = connection.createStatement();

      String s =
        "select employee_id,user_name,level_id,current_status from employee_level_mapping INNER JOIN employee  ON employee_level_mapping .employee_id = employee.id where level_id<='" +
        levelid +
        "' and employee_level_mapping.project_type_id='" +
        prtype +
        "' order by level_id";

      ResultSet rs = st.executeQuery(s);
      while (rs.next()) {
        levelemployee e = new levelemployee();
        e.setId(rs.getLong(1));
        e.setUsername(rs.getString(2));
        e.setLeid(rs.getLong(3));
        e.setCurrid(rs.getLong(4));
        String p =
          "select status_name from project_status where status_id='" +
          rs.getLong(4) +
          "'";
        ResultSet r = st.executeQuery(p);

        while (r.next()) {
          if (r.getString(1) == null) {
            System.out.println("noo");
          } else {
            System.out.println("yes");
            sname = r.getString(1);
          }
        }
        e.setSname(sname);
        System.out.println(e);
        a.add(e);
      }
      System.out.println(a);
    } catch (Exception e) {
      e.printStackTrace();
    }

    return a;
  }
}
