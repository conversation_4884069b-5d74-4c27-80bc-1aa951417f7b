package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.Instant;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "vra_dist_vacancy_mst_bkup")
public class VraDistVacancyMstBkup implements Serializable {
	
	private static final long serialVersionUID = 8976223234274705189L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DIST_VACANCY_SEQUENCE")
	@SequenceGenerator(name = "DIST_VACANCY_SEQUENCE", sequenceName = "DIST_VACANCY_SEQUENCE", allocationSize = 1)
	private Long id;
	
	@Column(name = "sno")
	private Long sno;
	
	@Column(name = "post_category")
	private String postCategoryName;
	
	@Column(name = "total_vacancies")
	private Long totalVacancies;
	
	@Column(name = "dist_id")
	private Long distId;
	
	@Column(name = "hod_id")
	private Long hodId;
	
	@Column(name = "updated_ip")
	private String createdIp;
	
	@Column(name = "created_date")
	private Instant createdDate = Instant.now();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public String getPostCategoryName() {
		return postCategoryName;
	}

	public void setPostCategoryName(String postCategoryName) {
		this.postCategoryName = postCategoryName;
	}

	public Long getTotalVacancies() {
		return totalVacancies;
	}

	public void setTotalVacancies(Long totalVacancies) {
		this.totalVacancies = totalVacancies;
	}

	public Long getDistId() {
		return distId;
	}

	public void setDistId(Long distId) {
		this.distId = distId;
	}

	public String getCreatedIp() {
		return createdIp;
	}

	public void setCreatedIp(String createdIp) {
		this.createdIp = createdIp;
	}

	public Instant getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Instant createdDate) {
		this.createdDate = createdDate;
	}

	public Long getHodId() {
		return hodId;
	}

	public void setHodId(Long hodId) {
		this.hodId = hodId;
	}

	@Override
	public String toString() {
		return "VraDistVacancyMstBkup [id=" + id + ", sno=" + sno + ", postCategoryName=" + postCategoryName
				+ ", totalVacancies=" + totalVacancies + ", distId=" + distId + ", hodId=" + hodId + ", createdIp="
				+ createdIp + ", createdDate=" + createdDate + "]";
	}
}
