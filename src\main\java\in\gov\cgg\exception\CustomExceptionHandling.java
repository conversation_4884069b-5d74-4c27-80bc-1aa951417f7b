//package in.gov.cgg.exception;
//
//import java.io.IOException;
//import java.sql.SQLException;
//
//import javax.servlet.http.HttpServletRequest;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.core.annotation.AnnotationUtils;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.authentication.InternalAuthenticationServiceException;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import org.springframework.web.servlet.ModelAndView;
//
//@ControllerAdvice
//public class CustomExceptionHandling{
//
//	 public static final String DEFAULT_ERROR_VIEW = "error";
//
//	  @ExceptionHandler(value = Exception.class)
//	  public ModelAndView
//	  defaultErrorHandler(HttpServletRequest req, Exception e) throws Exception {
//		  System.err.println("Exception >>>>>>>>>>>>>>>>>>>");
//	    // If the exception is annotated with @ResponseStatus rethrow it and let
//	    // the framework handle it - like the OrderNotFoundException example
//	    // at the start of this post.
//	    // AnnotationUtils is a Spring Framework utility class.
//	    if (AnnotationUtils.findAnnotation(e.getClass(), ResponseStatus.class) != null)
//	      throw e;
//System.err.println("Exception >>>>>>>>>>>>>>>>>>>");
//	    // Otherwise setup and send the user to a default error-view.
//	    ModelAndView mav = new ModelAndView();
//	    mav.addObject("exception", e.getMessage());
//	    mav.addObject("url", req.getRequestURL());
//	    mav.setViewName(DEFAULT_ERROR_VIEW);
//	    return mav;
//	  }
//
//	
//}
