package in.gov.cgg.entity;
import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ts_mandal_master1")
public class TsMandalMaster implements Serializable {

	private static final long serialVersionUID = 379939559460913367L;

	@Id
	@Column(name = "mandal_code")
	private Integer mandalCode;
	
	@Column(name = "dist_code")
	private Integer distCode;
	
	@Column(name = "mandal_name")
	private String mandalName;
	
	@Column(name = "div_id")
	private Integer divId;

	public Integer getMandalCode() {
		return mandalCode;
	}

	public void setMandalCode(Integer mandalCode) {
		this.mandalCode = mandalCode;
	}

	public Integer getDistCode() {
		return distCode;
	}

	public void setDistCode(Integer distCode) {
		this.distCode = distCode;
	}

	
	public Integer getDivId() {
		return divId;
	}

	public void setDivId(Integer divId) {
		this.divId = divId;
	}

	public String getMandalName() {
		return mandalName;
	}

	public void setMandalName(String mandalName) {
		this.mandalName = mandalName;
	}

	@Override
	public String toString() {
		return "TsMandalMaster [mandalCode=" + mandalCode + ", distCode=" + distCode + ", mandalName=" + mandalName
				+ ", divId=" + divId + "]";
	}

	

}
