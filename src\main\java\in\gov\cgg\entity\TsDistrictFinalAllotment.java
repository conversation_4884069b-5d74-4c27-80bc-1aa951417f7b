package in.gov.cgg.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name="vra_district_final_allotment")
public class TsDistrictFinalAllotment implements Serializable {

	private static final long serialVersionUID = 1874347319518898894L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DIST_ALLOT_SEQUENCE")
	@SequenceGenerator(name = "DIST_ALLOT_SEQUENCE", sequenceName = "DIST_ALLOT_SEQUENCE", allocationSize = 1)
	private Long sno;
	
	@Column(name = "district_code")
	private Integer districtCode;
	
	@Column(name = "district_name")
	private String districtName;
	
	@Column(name = "district_collector_name")
	private String districtCollectorName;
	
	@Column(name = "vra_status")
	private Integer vraStatus = 0;
	
	@Column(name = "vra_status_updated_time")
	private LocalDateTime vraStatusUpdatedTime;
	
	@Column(name = "vacancy_status")
	private Integer vacancyStatus = 0;
	
	@Column(name = "vacancy_status_updated_time")
	private LocalDateTime vacancyStatusUpdatedTime;
	
	@Column(name = "allotment_status")
	private Integer allotmentStatus = 0;
	
	@Column(name = "allotment_status_updated_time")
	private LocalDateTime allotmentStatusUpdatedTime;
	
	@Column(name = "final_allotment_status")
	private Integer finalAllotmentStatus = 0;
	
	@Column(name = "final_allotment_status_updated_time")
	private LocalDateTime finalAllotmentStatusUpdatedTime;
	
	@Column(name = "proceeding_no")
	private String proceedingNo;
	
	@DateTimeFormat(pattern = "dd/MM/yyyy")
	@Column(name="proceeding_date")
	private Date proceedingDate;

	public Long getSno() {
		return sno;
	}

	public void setSno(Long sno) {
		this.sno = sno;
	}

	public Integer getDistrictCode() {
		return districtCode;
	}

	public void setDistrictCode(Integer districtCode) {
		this.districtCode = districtCode;
	}

	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}

	public String getDistrictCollectorName() {
		return districtCollectorName;
	}

	public void setDistrictCollectorName(String districtCollectorName) {
		this.districtCollectorName = districtCollectorName;
	}

	public Integer getVraStatus() {
		return vraStatus;
	}

	public void setVraStatus(Integer vraStatus) {
		this.vraStatus = vraStatus;
	}

	public LocalDateTime getVraStatusUpdatedTime() {
		return vraStatusUpdatedTime;
	}

	public void setVraStatusUpdatedTime(LocalDateTime vraStatusUpdatedTime) {
		this.vraStatusUpdatedTime = vraStatusUpdatedTime;
	}

	public Integer getVacancyStatus() {
		return vacancyStatus;
	}

	public void setVacancyStatus(Integer vacancyStatus) {
		this.vacancyStatus = vacancyStatus;
	}

	public LocalDateTime getVacancyStatusUpdatedTime() {
		return vacancyStatusUpdatedTime;
	}

	public void setVacancyStatusUpdatedTime(LocalDateTime vacancyStatusUpdatedTime) {
		this.vacancyStatusUpdatedTime = vacancyStatusUpdatedTime;
	}

	public Integer getAllotmentStatus() {
		return allotmentStatus;
	}

	public void setAllotmentStatus(Integer allotmentStatus) {
		this.allotmentStatus = allotmentStatus;
	}

	public LocalDateTime getAllotmentStatusUpdatedTime() {
		return allotmentStatusUpdatedTime;
	}

	public void setAllotmentStatusUpdatedTime(LocalDateTime allotmentStatusUpdatedTime) {
		this.allotmentStatusUpdatedTime = allotmentStatusUpdatedTime;
	}

	public String getProceedingNo() {
		return proceedingNo;
	}

	public void setProceedingNo(String proceedingNo) {
		this.proceedingNo = proceedingNo;
	}

	public Date getProceedingDate() {
		return proceedingDate;
	}

	public void setProceedingDate(Date proceedingDate) {
		this.proceedingDate = proceedingDate;
	}

	public Integer getFinalAllotmentStatus() {
		return finalAllotmentStatus;
	}

	public void setFinalAllotmentStatus(Integer finalAllotmentStatus) {
		this.finalAllotmentStatus = finalAllotmentStatus;
	}

	public LocalDateTime getFinalAllotmentStatusUpdatedTime() {
		return finalAllotmentStatusUpdatedTime;
	}

	public void setFinalAllotmentStatusUpdatedTime(LocalDateTime finalAllotmentStatusUpdatedTime) {
		this.finalAllotmentStatusUpdatedTime = finalAllotmentStatusUpdatedTime;
	}

	@Override
	public String toString() {
		return "TsDistrictFinalAllotment [sno=" + sno + ", districtCode=" + districtCode + ", districtName="
				+ districtName + ", districtCollectorName=" + districtCollectorName + ", vraStatus=" + vraStatus
				+ ", vraStatusUpdatedTime=" + vraStatusUpdatedTime + ", vacancyStatus=" + vacancyStatus
				+ ", vacancyStatusUpdatedTime=" + vacancyStatusUpdatedTime + ", allotmentStatus=" + allotmentStatus
				+ ", allotmentStatusUpdatedTime=" + allotmentStatusUpdatedTime + ", finalAllotmentStatus="
				+ finalAllotmentStatus + ", finalAllotmentStatusUpdatedTime=" + finalAllotmentStatusUpdatedTime
				+ ", proceedingNo=" + proceedingNo + ", proceedingDate=" + proceedingDate + "]";
	}
	
	
	
}
