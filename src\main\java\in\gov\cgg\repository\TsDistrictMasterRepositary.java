package in.gov.cgg.repository;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.TsDistrictMaster;

@Repository
public interface TsDistrictMasterRepositary extends JpaRepository<TsDistrictMaster, Long>{
	
	@Override
	@Query
	public List<TsDistrictMaster> findAll();

	
}
