package in.gov.cgg.repository;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.TsVraPriorityMaster;

@Repository
public interface TsVraPriorityMstRepositary extends JpaRepository<TsVraPriorityMaster, Long>{

	List<TsVraPriorityMaster> findByVraSno(Long sno);

	@Modifying
	@Transactional
	@Query(value="delete from ts_vra_options_mst where vra_sno = ?1",nativeQuery=true)
	void deleteByVraSno(Long sno);
	
}
