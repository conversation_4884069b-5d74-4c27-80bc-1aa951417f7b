package in.gov.cgg.repository;

import java.util.List;
import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import in.gov.cgg.entity.VraDistVacancyAllotmentTemp;

@Repository
public interface VraDistVacAllotmentTempRepositary extends JpaRepository<VraDistVacancyAllotmentTemp, Long>{

	List<VraDistVacancyAllotmentTemp> findByDistId(Long distId);

	@Modifying
	@Transactional
	@Query(value="delete from vra_dist_vacancy_allotment_temp where dist_id = ?1",nativeQuery=true)
	Integer deleteByDistId(Long distId);
	
}
